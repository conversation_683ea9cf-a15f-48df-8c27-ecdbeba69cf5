FROM php:8.1-fpm

RUN apt-get update && apt-get install -y \
    nginx \
    && docker-php-ext-install pdo pdo_mysql

COPY ./nginx.conf /etc/nginx/conf.d/default.conf
COPY ./src /var/www/html

# Create log directories with proper permissions
RUN mkdir -p /var/log/php /var/log/nginx && \
    chown -R www-data:www-data /var/www/html /var/log/php /var/log/nginx && \
    chmod -R 755 /var/log/php /var/log/nginx

EXPOSE 80

CMD service nginx start && php-fpm