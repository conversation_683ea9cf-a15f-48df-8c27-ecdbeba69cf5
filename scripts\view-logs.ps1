# PowerShell script to view PHP error logs
param(
    [string]$LogType = "php",
    [int]$Lines = 50,
    [switch]$Follow
)

$LogsDir = Join-Path $PSScriptRoot "..\logs"

switch ($LogType.ToLower()) {
    "php" {
        $LogFile = Join-Path $LogsDir "php_errors.log"
        Write-Host "📋 Viewing PHP Error Log (last $Lines lines)" -ForegroundColor Green
    }
    "nginx" {
        $LogFile = Join-Path $LogsDir "access.log"
        Write-Host "📋 Viewing Nginx Access Log (last $Lines lines)" -ForegroundColor Green
    }
    "nginx-error" {
        $LogFile = Join-Path $LogsDir "error.log"
        Write-Host "📋 Viewing Nginx Error Log (last $Lines lines)" -ForegroundColor Green
    }
    default {
        Write-Host "❌ Invalid log type. Use: php, nginx, nginx-error" -ForegroundColor Red
        exit 1
    }
}

if (-not (Test-Path $LogFile)) {
    Write-Host "⚠️  Log file not found: $LogFile" -ForegroundColor Yellow
    Write-Host "💡 Make sure Docker containers are running and generating logs" -ForegroundColor Cyan
    exit 1
}

Write-Host "📁 Log file: $LogFile" -ForegroundColor Cyan
Write-Host "─" * 80 -ForegroundColor Gray

if ($Follow) {
    Write-Host "👀 Following log file (Ctrl+C to stop)..." -ForegroundColor Yellow
    Get-Content $LogFile -Tail $Lines -Wait
} else {
    Get-Content $LogFile -Tail $Lines
}

Write-Host "─" * 80 -ForegroundColor Gray
Write-Host "✅ Log viewing completed" -ForegroundColor Green
