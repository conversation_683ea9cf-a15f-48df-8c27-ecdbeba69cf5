<?php

return [
    // ===== SHARED COMPONENTS =====
    'shared' => [
        'table_of_contents' => 'Table of Contents',
        'header' => [
            'home' => 'Home',
            'login' => 'Login',
            'logout' => 'Logout',
            'profile' => 'Profile',
            'deposit' => 'Deposit',
            'history' => 'Transaction History',
            'balance' => 'Balance',
            'refresh_balance' => 'Refresh Balance',
        ],

        'footer' => [
            'all_rights_reserved' => '',
            'terms_of_service' => 'Terms of Service',
            'privacy_policy' => 'Privacy Policy',
            'contact_us' => 'Contact Us',
            'description' => 'Leading gaming account management system in Vietnam',
            'quick_links' => 'Quick Links',
            'support' => 'Support',
            'support_24_7' => '24/7 Support',
            'secure_payment' => 'Secure Payment',
            'version' => 'Version',
        ],

        'language' => [
            'vietnamese' => 'Tiếng Việt',
            'english' => 'English',
        ],

        'table_of_contents' => 'Table of Contents',

        'banners' => [
            'special_promotion' => 'Special Promotion',
            'deposit_now' => 'Deposit Now',
            'new_game_launch' => 'New Game Launch',
            'play_now' => 'Play Now',
            'weekly_tournament' => 'Weekly Tournament',
            'join_now' => 'Join Now',
            'bonus_offer' => 'Get 50% extra bonus with many attractive offers',
            'new_game_desc' => 'Explore the RPG Fantasy world with stunning graphics and top-tier gameplay',
            'tournament_desc' => 'Join the tournament and win prizes up to 10 million VND with many valuable gifts',
        ],

        'errors' => [
            'page_not_found' => 'Page Not Found',
            'page_not_found_message' => 'The page you are looking for does not exist or has been moved.',
            'server_error' => 'Server Error',
            'server_error_message' => 'An unexpected error occurred. Please try again later.',
            'back' => 'Back',
            'try_again' => 'Try Again',
        ],

        'validation' => [
            'required' => 'This field is required',
            'email_invalid' => 'Invalid email format (e.g., <EMAIL>)',
            'phone_invalid' => 'Phone number must have 10 digits and start with 0',
            'username_min' => 'Username must be at least 6 characters',
            'username_max' => 'Username cannot exceed 20 characters',
            'username_format' => 'Only letters, numbers and underscores allowed',
            'username_no_spaces' => 'Username cannot contain spaces',
            'password_min' => 'Password must be at least 6 characters',
            'password_max' => 'Password cannot exceed 30 characters',
            'password_enter_first' => 'Please enter password first',
            'password_mismatch' => 'Password confirmation does not match',
        ],

        'balance' => [
            'wait_seconds' => 'Please wait {seconds} seconds before refreshing again',
            'updated' => 'Balance has been updated',
            'update_failed' => 'Failed to update balance',
            'connection_error' => 'Connection error',
        ],

        'register_js' => [
            'hide_contact_info' => 'Hide contact information',
            'add_contact_info' => 'Add contact information (optional)',
            'password_mismatch' => 'Password confirmation does not match',
            'creating_account' => 'CREATING...',
            'firebase_not_initialized' => 'Firebase not initialized',
            'google_register_failed' => 'Google registration failed',
            'facebook_register_failed' => 'Facebook registration failed',
            'error_occurred' => 'An error occurred. Please try again.',
        ],

        'login_js' => [
            'firebase_not_initialized' => 'Firebase not initialized',
            'google_login_failed' => 'Google login failed',
            'facebook_login_failed' => 'Facebook login failed',
            'logging_in' => 'LOGGING IN...',
            'login' => 'LOGIN',
            'login_button' => 'LOGIN',
            'error_occurred' => 'An error occurred. Please try again.',
        ],

        'profile_js' => [
            'member_since' => 'Member since',
            'current_balance' => 'Current balance',
            'verification_status' => 'Verification status',
            'email' => 'Email',
            'phone' => 'Phone',
            'verified' => 'Verified',
            'not_verified' => 'Not verified',
            'deposit' => 'Deposit',
            'transaction_history' => 'Transaction history',
            'personal_info' => 'Personal information',
            'account_verification' => 'Account verification',
            'username' => 'Username',
            'email_verification' => 'Email verification',
            'email_verification_desc' => 'Verify your email address to enhance account security',
            'email_address' => 'Email address',
            'enter_email' => 'Enter email address',
            'send_verification_code' => 'Send verification code',
            'verification_code' => 'Verification code',
            'enter_verification_code' => 'Enter verification code from email',
            'verify' => 'Verify',
            'check_email_instruction' => 'Please check your email inbox and enter the verification code',
            'email_verified_success' => 'Email has been verified successfully',
            'phone_verification' => 'Phone verification',
            'phone_verification_desc' => 'Verify your phone number to receive notifications and enhance security',
            'phone_number' => 'Phone number',
            'enter_phone' => 'Enter phone number',
            'send_otp' => 'Send OTP',
            'otp_code' => 'OTP code',
            'enter_otp' => 'Enter OTP from SMS',
            'check_sms_instruction' => 'Please check your SMS and enter the OTP code',
            'phone_verified_success' => 'Phone number has been verified successfully',
            'account_security' => 'Account security',
            'change_password' => 'Change password',
            'change_password_desc' => 'Update your password to secure your account',
            'current_password' => 'Current password',
            'enter_current_password' => 'Enter current password',
            'new_password' => 'New password',
            'enter_new_password' => 'Enter new password',
            'password_length_requirement' => 'Password must be 6-30 characters',
            'confirm_new_password' => 'Confirm new password',
            'enter_confirm_password' => 'Re-enter new password',
            'security_info_title' => 'Account security',
            'security_info_desc' => 'To enhance security, use a strong password and verify your email/phone in the "Account Verification" tab.',
            'enter_current_password_error' => 'Please enter current password',
            'enter_new_password_error' => 'Please enter new password',
            'password_length_error' => 'New password must be 6-30 characters',
            'confirm_password_error' => 'Please confirm new password',
            'password_mismatch' => 'Password confirmation does not match',
            'changing_password' => 'Changing password...',
            'enter_email_error' => 'Please enter email address',
            'sending' => 'Sending...',
            'resend_in' => 'Resend in',
            'enter_email_and_code' => 'Please enter email and verification code',
            'verifying' => 'Verifying...',
            'enter_phone_error' => 'Please enter phone number',
            'enter_phone_and_otp' => 'Please enter phone number and OTP code',
            'error_occurred' => 'An error occurred. Please try again.',
        ],

        'forgot_password_js' => [
            'click_to_refresh' => 'Click to refresh',
            'refresh_captcha' => 'Refresh captcha',
            'captcha_instruction' => 'Enter 5 characters shown in image (case insensitive)',
            'remember_password' => 'Remember your password?',
            'login_now' => 'Login now',
            'sending' => 'Sending...',
            'captcha_refresh_failed' => 'Unable to refresh captcha',
            'captcha_error_check' => 'Captcha',
            'error_occurred' => 'An error occurred. Please try again.',
        ],

        'password_strength' => [
            'weak' => 'weak',
            'medium' => 'medium',
            'strong' => 'strong',
            'very_strong' => 'very strong',
            'need_more' => 'need more',
            'good' => 'good',
            'min_8_chars' => 'at least 8 characters',
            'uppercase' => 'uppercase letter',
            'lowercase' => 'lowercase letter',
            'number' => 'number',
            'special_char' => 'special character',
        ],
    ],

    // ===== ID DOMAIN =====
    'auth' => [
        'login' => [
            'view' => [
                'title' => 'Login to Your Account',
                'subtitle' => 'Sign in to access your account',
                'quick_login' => 'Quick Login',
                'or_login_with_account' => 'or login with account',
                'username' => 'Username',
                'password' => 'Password',
                'enter_username' => 'Enter your username',
                'enter_password' => 'Enter your password',
                'remember_me' => 'Remember Me',
                'forgot_password' => 'Forgot Password',
                'dont_have_account' => "Don't have an account?",
                'create_account' => 'Register Now',
            ],
            'controller' => [
                'success' => 'Login successful',
                'invalid_credentials' => 'Invalid username or password',
                'account_locked' => 'Account has been locked',
                'too_many_attempts' => 'Too many attempts. Please try again in {minutes} minutes',
                'validation_required' => 'Please fill in all required fields',
                'captcha_invalid' => 'Invalid captcha code',
                'invalid_method' => 'Invalid login method',
                'invalid_token' => 'Invalid security token',
            ],
        ],
        'forgot_password' => [
            'view' => [
                'title' => 'Forgot Password',
                'subtitle' => 'Enter your email to recover your password',
                'email' => 'Email',
                'enter_email' => 'Enter your email address',
                'captcha' => 'Captcha',
                'enter_captcha' => 'Enter captcha code',
                'send_reset_link' => 'Send Reset Link',
                'click_to_refresh' => 'Click to refresh',
                'refresh_captcha' => 'Refresh captcha',
                'captcha_instruction' => 'Enter 5 characters shown in image (case insensitive)',
                'remember_password' => 'Remember your password?',
                'login_now' => 'Login now',
            ],
            'controller' => [
                'email_sent' => 'Password reset link has been sent to your email',
                'email_not_found' => 'No account found with this email address',
                'rate_limit' => 'Please wait {minutes} minutes before sending again',
                'invalid_email' => 'Invalid email address',
                'captcha_invalid' => 'Invalid captcha code',
            ],
        ],
        'register' => [
            'view' => [
                'title' => 'Register',
                'subtitle' => 'Create a new account to get started',
                'quick_register' => 'Quick Register',
                'or_register_with_account' => 'or register with account',
                'username' => 'Username',
                'enter_username' => 'Enter username',
                'password' => 'Password',
                'enter_password' => 'Enter password',
                'confirm_password' => 'Confirm Password',
                'enter_confirm_password' => 'Re-enter password',
                'email' => 'Email',
                'phone' => 'Phone Number',
                'add_contact_info' => 'Add contact information (optional)',
                'hide_contact_info' => 'Hide contact information',
                'agree_terms' => 'I agree to the',
                'terms_of_service' => 'Terms of Service',
                'create_account' => 'CREATE ACCOUNT',
                'already_have_account' => 'Already have an account?',
                'login_now' => 'Login now',
                'terms_agreement' => 'By registering, you agree to',
                'terms_link' => 'Terms of Service',
                'and' => 'and',
                'privacy_link' => 'Privacy Policy',
                'of_mtfgame' => 'of MTF Game',
            ],
            'controller' => [
                'success' => 'Registration successful',
                'username_exists' => 'Username already exists',
                'email_exists' => 'Email already in use',
                'phone_exists' => 'Phone number already in use',
                'validation_required' => 'Invalid data',
                'invalid_token' => 'Invalid security token',
                'invalid_method' => 'Invalid registration method',
                'password_mismatch' => 'Password confirmation does not match',
            ],
        ]
    ],


    'profile' => [
        'view' => [
            'title' => 'Profile',
            'subtitle' => 'Manage personal information and account settings',
            'personal_info' => 'Personal Information',
            'account_verification' => 'Account Verification',
            'security' => 'Security',
            'email_verification' => 'Email Verification',
            'phone_verification' => 'Phone Verification',
            'verified' => 'Verified',
            'not_verified' => 'Not Verified',
            'change_password' => 'Change Password',
        ],
        'controller' => [
            'email_sent' => 'Verification code has been sent to your email',
            'phone_sent' => 'Verification code has been sent to your phone',
            'email_verified' => 'Email verification successful',
            'phone_verified' => 'Phone verification successful',
            'password_changed' => 'Password changed successfully',
            'password_changed_login_required' => 'Password changed successfully. Please login again to continue.',
            'password_mismatch' => 'Password confirmation does not match',
            'invalid_token' => 'Invalid security token',
            'validation_required' => 'Invalid data',
        ],
    ],

    'history' => [
        'view' => [
            'title' => 'Transaction History',
            'subtitle' => 'Track all your transactions',
            'transaction_id' => 'Transaction ID',
            'transaction_type' => 'Type',
            'transaction_status' => 'Status',
            'transaction_date' => 'Time',
            'amount' => 'Amount',
            'payment_method' => 'Method',
            'balance_after' => 'Balance After',
            'pending' => 'Pending',
            'completed' => 'Completed',
            'failed' => 'Failed',
            'total_deposits' => 'Total Deposits',
            'total_spent' => 'Total Spent',
            'current_balance' => 'Current Balance',
            'all_types' => 'All Types',
            'all_status' => 'All Status',
            'deposit' => 'Deposit',
            'purchase' => 'Purchase',
            'filter' => 'Filter',
            'from_date' => 'From Date',
            'to_date' => 'To Date',
            'showing' => 'Showing',
            'transactions' => 'transactions',
        ],
    ],

    // ===== PAY DOMAIN =====
    'home' => [
        'view' => [
            'select_product' => 'Select Product',
            'explore_games' => 'Explore diverse and exciting gaming worlds',
        ],
    ],

    'deposit' => [
        'view' => [
            'title' => 'Deposit',
            'select_package' => 'Choose the right deposit package',
            'select_amount' => 'Select deposit amount',
            'payment_method' => 'Choose payment method',
            'info' => 'Information',
            'auto_processing' => 'Auto processing',
            'absolute_security' => 'Absolute security',
            'need_support' => 'Need support',
            'support_message' => 'Contact us if you have difficulties during the deposit process.',
            'contact_24_7' => 'Contact 24/7',
        ],
        'controller' => [
            'success' => 'Deposit successful. Balance has been updated',
            'pending' => 'Transaction is being processed. Please wait a moment',
            'failed' => 'Transaction failed. Please try again',
            'invalid_amount' => 'Invalid deposit amount',
            'minimum_amount' => 'Minimum deposit amount is {amount} VND',
            'payment_method_required' => 'Please select a payment method',
            'insufficient_balance' => 'Insufficient balance to complete transaction',
        ],
    ],

    'game_guide' => [
        'view' => [
            'title' => '{game_name} Deposit Guide',
            'breadcrumb_home' => 'Home',
            'breadcrumb_deposit' => 'Game Deposit',
            'step_1_title' => 'Step 1: Deposit Lua into your account',
            'step_2_title' => 'Step 2: After having Lua, enter {game_name} game, select packages to purchase and pay with Lua',
            'instruction_text' => 'Select packages in the game and pay with Lua available in your account',
            'deposit_now' => 'Deposit Lua Now',
        ],
    ],

    // ===== POLICY PAGES =====
    'policy' => [
        'terms' => [
            'view' => [
                'title' => 'Terms of Service',
                'subtitle' => 'Please read these terms carefully before using our services',
                'last_updated' => 'Last updated',
                'acceptance_title' => '1. Acceptance of Terms',
                'acceptance_content' => 'By accessing and using the MTF Game website, you agree to be bound by these terms and conditions of use.',
                'service_title' => '2. Service Description',
                'service_content' => 'MTF Game provides an online gaming platform with the following services:',
                'service_item_1' => 'Game accounts and personal information management',
                'service_item_2' => 'Online payment and deposit systems',
                'service_item_3' => '24/7 customer support services',
                'service_item_4' => 'Gaming features and online entertainment',
                'account_title' => '3. User Accounts',
                'account_content' => 'To use our services, you must create an account and agree to:',
                'account_item_1' => 'Provide accurate and complete information',
                'account_item_2' => 'Keep your login credentials secure',
                'account_item_3' => 'Not share your account with others',
                'account_item_4' => 'Report immediately if your account is compromised',
                'payment_title' => '4. Payment and Refunds',
                'payment_content' => 'Payment regulations:',
                'payment_item_1' => 'All transactions are processed through secure systems',
                'payment_item_2' => 'No refunds after successful transactions',
                'payment_item_3' => 'Contact support within 24 hours if issues arise',
                'payment_item_4' => 'Comply with all payment-related laws and regulations',
                'prohibited_title' => '5. Prohibited Conduct',
                'prohibited_content' => 'The following behaviors are strictly prohibited:',
                'prohibited_item_1' => 'Using hacks, cheats, or bot software',
                'prohibited_item_2' => 'Spam or unauthorized advertising',
                'prohibited_item_3' => 'Harassment or abuse of other users',
                'prohibited_item_4' => 'Sharing inappropriate content',
                'prohibited_item_5' => 'Violating Vietnamese laws',
                'liability_title' => '6. Liability',
                'liability_content' => 'MTF Game is not responsible for indirect, incidental, or consequential damages arising from service use. Users are responsible for their own actions.',
                'changes_title' => '7. Changes to Terms',
                'changes_content' => 'We reserve the right to update these terms at any time. Continued use of the service after changes constitutes acceptance of the new terms.',
                'contact_title' => '8. Contact',
                'contact_content' => 'If you have questions about these terms, please contact:',
                'back_home' => 'Back to Home',
                'privacy_policy' => 'Privacy Policy',
                'contact_us' => 'Contact Us',
            ],
        ],

        'privacy' => [
            'view' => [
                'title' => 'Privacy Policy',
                'subtitle' => 'Our commitment to protecting your personal information',
                'last_updated' => 'Last updated',
                'intro_title' => '1. Introduction',
                'intro_content' => 'MTF Game is committed to protecting the privacy and personal information of our users. This policy describes how we collect, use, and protect your information.',
                'collection_title' => '2. Information We Collect',
                'collection_content' => 'We collect the following types of information:',
                'personal_info_title' => 'Personal Information:',
                'personal_info_1' => 'Username and password',
                'personal_info_2' => 'Email address and phone number',
                'personal_info_3' => 'Payment information (encrypted)',
                'personal_info_4' => 'Other contact information (if provided)',
                'usage_info_title' => 'Usage Information:',
                'usage_info_1' => 'IP address and device information',
                'usage_info_2' => 'Access history and activity logs',
                'usage_info_3' => 'Cookies and session data',
                'usage_info_4' => 'Browser information',
                'usage_title' => '3. How We Use Information',
                'usage_content' => 'Information is used to:',
                'usage_item_1' => 'Provide and improve our services',
                'usage_item_2' => 'Process transactions and payments',
                'usage_item_3' => 'Provide customer support',
                'usage_item_4' => 'Send important notifications',
                'usage_item_5' => 'Analyze usage and generate statistics',
                'sharing_title' => '4. Information Sharing',
                'sharing_content' => 'We do not sell or rent personal information. Information is only shared in the following cases:',
                'sharing_item_1' => 'With user consent',
                'sharing_item_2' => 'When required by law enforcement',
                'sharing_item_3' => 'To protect MTF Game\'s rights and interests',
                'sharing_item_4' => 'With service partners (under confidentiality agreements)',
                'security_title' => '5. Information Security',
                'security_content' => 'We implement the following security measures:',
                'security_item_1' => 'SSL/TLS encryption for all data',
                'security_item_2' => 'Firewall and intrusion prevention systems',
                'security_item_3' => 'Strict access controls',
                'security_item_4' => 'Regular data backups',
                'cookies_title' => '6. Cookies',
                'cookies_content' => 'Our website uses cookies to:',
                'cookies_item_1' => 'Remember login information',
                'cookies_item_2' => 'Improve user experience',
                'cookies_item_3' => 'Analyze website traffic',
                'rights_title' => '7. Your Rights',
                'rights_content' => 'You have the right to:',
                'rights_item_1' => 'Access and update personal information',
                'rights_item_2' => 'Request account and data deletion',
                'rights_item_3' => 'Opt out of marketing emails',
                'rights_item_4' => 'File complaints about data processing',
                'data_deletion_title' => 'How to Delete Personal Data:',
                'data_deletion_step_1' => 'Log in to your account',
                'data_deletion_step_2' => 'Go to "Account Settings" or "Profile"',
                'data_deletion_step_3' => 'Select "Delete Account" or contact support',
                'data_deletion_step_4' => 'Confirm request via email',
                'data_deletion_note' => 'Note: After deletion, data cannot be recovered. Some information may be retained as required by law.',
                'contact_title' => '8. Contact',
                'contact_content' => 'If you have questions about this privacy policy:',
                'back_home' => 'Back to Home',
                'terms_service' => 'Terms of Service',
                'contact_us' => 'Contact Us',
            ],
        ],

        'contact' => [
            'view' => [
                'title' => 'Contact Support',
                'subtitle' => 'We are always ready to help you',
                'form_title' => 'Send Message',
                'name' => 'Full Name',
                'enter_name' => 'Enter your full name',
                'email' => 'Email',
                'enter_email' => 'Enter your email address',
                'subject' => 'Subject',
                'select_subject' => 'Select subject',
                'general_inquiry' => 'General Inquiry',
                'technical_support' => 'Technical Support',
                'payment_issue' => 'Payment Issue',
                'account_issue' => 'Account Issue',
                'suggestion' => 'Suggestion',
                'other' => 'Other',
                'message' => 'Message',
                'enter_message' => 'Enter your message',
                'send_message' => 'Send Message',
                'sending' => 'Sending',
                'contact_info' => 'Contact Information',
                'email_support' => 'Email Support',
                'email_response_time' => 'Response within 24 hours',
                'phone_support' => 'Phone Support',
                'phone_hours' => '24/7 Support',
                'office_address' => 'Office Address',
                'office_hours' => 'Monday - Sunday: 8:00 AM - 10:00 PM',
                'faq_title' => 'Frequently Asked Questions',
                'faq_1_question' => 'How do I deposit money into my account?',
                'faq_1_answer' => 'You can deposit money through the "Deposit" section with various payment methods available.',
                'faq_2_question' => 'I forgot my password, what should I do?',
                'faq_2_answer' => 'Use the "Forgot Password" feature on the login page to recover your account.',
                'faq_3_question' => 'How do I contact support?',
                'faq_3_answer' => 'You can send an email, call our hotline, or use the contact form on this page.',
                'back_home' => 'Back to Home',
                'terms_service' => 'Terms of Service',
                'privacy_policy' => 'Privacy Policy',
                'error_occurred' => 'An error occurred. Please try again.',
            ],
            'controller' => [
                'name_required' => 'Please enter your full name',
                'email_invalid' => 'Invalid email address',
                'subject_required' => 'Please select a subject',
                'message_required' => 'Please enter a message',
                'message_sent' => 'Message sent successfully. We will respond as soon as possible.',
            ],
        ],
    ],
];
