<?php

namespace Shared\Controllers\Api;

use Core\Controller;
use Shared\Services\ApiService;

/**
 * Balance API Controller - Shared between domains
 */
class BalanceController extends Controller
{
    private ApiService $apiService;
    private const RATE_LIMIT_SECONDS = 3; // Minimum 3 seconds between requests

    public function __construct()
    {
        parent::__construct();
        $this->apiService = new ApiService();
    }

    /**
     * Refresh user balance
     */
    public function refresh(): void
    {
        // Clean output buffer to prevent extra characters
        if (ob_get_level()) {
            ob_clean();
        }

        // Set security headers
        header('Content-Type: application/json');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');

        try {
            // 1. Validate HTTP method
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                $this->jsonError('Method not allowed', 405);
                return;
            }

            // 2. Check authentication
            if (!$this->isAuthenticated()) {
                $this->jsonError('Unauthorized', 401);
                return;
            }

            // 3. Validate CSRF token
            $csrfToken = $this->input('csrf_token');
            if (!$this->securityHelper->verifyCsrfToken($csrfToken)) {
                $this->jsonError('Invalid security token', 403);
                return;
            }

            // 4. Rate limiting check
            if (!$this->checkRateLimit()) {
                $this->jsonError('Too many requests. Please wait before refreshing again.', 429);
                return;
            }

            // 5. Validate request origin (basic check)
            if (!$this->validateRequestOrigin()) {
                $this->jsonError('Invalid request origin', 403);
                return;
            }

            // 6. Validate session integrity
            $user = $this->getUser();
            if (!$user || !isset($user['user_id'])) {
                $this->jsonError('Invalid session data', 401);
                return;
            }

            // 7. Get access token with validation
            $accessToken = $this->getAccessToken();
            if (!$accessToken) {
                $this->jsonError('Missing access token', 401);
                return;
            }

            // 8. Get fresh balance from API
            $response = $this->apiService->verifyToken($accessToken);
            
            if (!isset($response['user']) || !is_array($response['user'])) {
                $this->jsonError('Invalid API response', 500);
                return;
            }

            $userData = $response['user'];

            // 9. Validate API response data
            if (!isset($userData['balance']) || !is_numeric($userData['balance'])) {
                $this->jsonError('Invalid balance data from API', 500);
                return;
            }

            // 10. Update session with fresh data
            $_SESSION['user'] = array_merge($_SESSION['user'], [
                'balance' => (float)$userData['balance'],
                'last_balance_update' => time()
            ]);

            // 11. Update rate limit timestamp
            $_SESSION['last_balance_refresh'] = time();

            // 12. Format balance for display
            $balance = (float)$userData['balance'];

            $this->jsonSuccess([
                'balance' => $balance,
                'formatted_balance' => number_format($balance, 0, ',', '.'),
                'last_updated' => time()
            ], 'Balance updated successfully');

        } catch (\Exception $e) {
            error_log('Balance refresh error: ' . $e->getMessage() . ' | User: ' . ($user['user_id'] ?? 'unknown') . ' | IP: ' . $this->getRealIpAddress());
            
            $this->jsonError('Failed to refresh balance. Please try again.', 500);
        }
    }

    /**
     * Check rate limiting for balance refresh
     */
    private function checkRateLimit(): bool
    {
        $lastRefresh = $_SESSION['last_balance_refresh'] ?? 0;
        $timeDiff = time() - $lastRefresh;
        
        return $timeDiff >= self::RATE_LIMIT_SECONDS;
    }

    /**
     * Validate request origin (basic security check)
     */
    private function validateRequestOrigin(): bool
    {
        // Check if request is from same domain or allowed origins
        $allowedOrigins = [
            $this->getDomainUrl('id'),
            $this->getDomainUrl('pay')
        ];

        $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
        $referer = $_SERVER['HTTP_REFERER'] ?? '';

        // Allow requests without origin/referer (direct API calls from same domain)
        if (empty($origin) && empty($referer)) {
            return true;
        }

        // Check origin
        if (!empty($origin)) {
            return in_array($origin, $allowedOrigins);
        }

        // Check referer
        if (!empty($referer)) {
            foreach ($allowedOrigins as $allowedOrigin) {
                if (strpos($referer, $allowedOrigin) === 0) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Return JSON error response
     */
    private function jsonError(string $message, int $statusCode = 400): void
    {
        http_response_code($statusCode);
        echo json_encode([
            'status' => false,
            'message' => $message,
            'timestamp' => time()
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * Return JSON success response
     */
    private function jsonSuccess(array $data, string $message = 'Success'): void
    {
        echo json_encode([
            'status' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => time()
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
}
