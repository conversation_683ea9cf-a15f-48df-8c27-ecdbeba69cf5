<?php

$this->startSection('content');

$domain = $domainData ?? [];
$pay = $domainData['domains']['pay'] ?? "";
?>

<div class="bg-gray-50 py-4 sm:py-6 lg:py-8">
    <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-4 sm:mb-6 lg:mb-8">
            <h1 class="text-2xl sm:text-3xl font-gaming font-bold text-gray-900 mb-2"><?= $__('profile.view.title') ?></h1>
            <p class="text-gray-600 text-sm sm:text-base"><?= $__('profile.view.subtitle') ?></p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg sm:rounded-xl shadow-lg border border-gray-200 p-4 sm:p-6">
                    <!-- User Avatar -->
                    <div class="text-center mb-4 sm:mb-6">
                        <div class="w-20 h-20 sm:w-24 sm:h-24 bg-gradient-to-r from-blue-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-lg">
                            <i class="fas fa-user text-white text-xl sm:text-2xl"></i>
                        </div>
                        <h3 class="text-lg sm:text-xl font-gaming font-bold text-gray-900"><?= $this->escape($user['username']) ?></h3>
                        <p class="text-gray-600 text-xs sm:text-sm"><?= $__('shared.profile_js.member_since') ?> <?= date('d/m/Y', strtotime($user['created_at'] ?? 'now')) ?></p>
                    </div>

                    <!-- Account Stats -->
                    <div class="space-y-3 sm:space-y-4">
                        <div class="bg-gradient-to-r from-blue-50 to-emerald-50 rounded-lg p-3 sm:p-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-xs sm:text-sm text-gray-600"><?= $__('shared.profile_js.current_balance') ?></p>
                                    <p class="text-lg sm:text-xl font-bold text-emerald-600"><?= $this->currency($user['balance'] ?? 0, 'medium') ?></p>
                                </div>
                                <div class="w-10 h-10 sm:w-12 sm:h-12 bg-emerald-500 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <i class="fas fa-gem text-white text-sm sm:text-base"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Verification Status -->
                        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-3 sm:p-4">
                            <h4 class="text-xs sm:text-sm font-medium text-gray-700 mb-2 sm:mb-3"><?= $__('shared.profile_js.verification_status') ?></h4>
                            <div class="space-y-1.5 sm:space-y-2">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center min-w-0">
                                        <i class="fas fa-envelope text-gray-400 mr-1.5 sm:mr-2 text-xs sm:text-sm flex-shrink-0"></i>
                                        <span class="text-xs sm:text-sm text-gray-600 truncate"><?= $__('shared.profile_js.email') ?></span>
                                    </div>
                                    <?php if ($user['is_email_verified'] ?? false): ?>
                                        <span class="inline-flex items-center px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 flex-shrink-0">
                                            <i class="fas fa-check-circle mr-1"></i>
                                            <span class="hidden sm:inline"><?= $__('shared.profile_js.verified') ?></span>
                                            <span class="sm:hidden">✓</span>
                                        </span>
                                    <?php else: ?>
                                        <span class="inline-flex items-center px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 flex-shrink-0">
                                            <i class="fas fa-times-circle mr-1"></i>
                                            <span class="hidden sm:inline"><?= $__('shared.profile_js.not_verified') ?></span>
                                            <span class="sm:hidden">✗</span>
                                        </span>
                                    <?php endif; ?>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center min-w-0">
                                        <i class="fas fa-phone text-gray-400 mr-1.5 sm:mr-2 text-xs sm:text-sm flex-shrink-0"></i>
                                        <span class="text-xs sm:text-sm text-gray-600 truncate"><?= $__('shared.profile_js.phone') ?></span>
                                    </div>
                                    <?php if ($user['is_phone_verified'] ?? false): ?>
                                        <span class="inline-flex items-center px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 flex-shrink-0">
                                            <i class="fas fa-check-circle mr-1"></i>
                                            <span class="hidden sm:inline"><?= $__('shared.profile_js.verified') ?></span>
                                            <span class="sm:hidden">✓</span>
                                        </span>
                                    <?php else: ?>
                                        <span class="inline-flex items-center px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 flex-shrink-0">
                                            <i class="fas fa-times-circle mr-1"></i>
                                            <span class="hidden sm:inline"><?= $__('shared.profile_js.not_verified') ?></span>
                                            <span class="sm:hidden">✗</span>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="mt-4 sm:mt-6 space-y-2 sm:space-y-3">
                        <a href="<?= $pay ?>" class="w-full bg-gradient-to-r from-emerald-500 to-blue-600 text-white py-2.5 sm:py-3 px-3 sm:px-4 rounded-lg font-semibold hover:from-emerald-600 hover:to-blue-700 transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center justify-center text-sm sm:text-base">
                            <i class="fas fa-plus mr-1.5 sm:mr-2 text-sm sm:text-base"></i>
                            <?= $__('shared.profile_js.deposit') ?>
                        </a>
                        <a href="<?= $this->url('/history') ?>" class="w-full bg-gray-100 text-gray-700 py-2.5 sm:py-3 px-3 sm:px-4 rounded-lg font-semibold hover:bg-gray-200 transition-colors flex items-center justify-center text-sm sm:text-base">
                            <i class="fas fa-history mr-1.5 sm:mr-2 text-sm sm:text-base"></i>
                            <?= $__('shared.profile_js.transaction_history') ?>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg sm:rounded-xl shadow-lg border border-gray-200">
                    <!-- Tabs -->
                    <div class="border-b border-gray-200">
                        <nav class="flex space-x-4 sm:space-x-6 lg:space-x-8 px-4 sm:px-6 overflow-x-auto">
                            <button class="profile-tab active py-3 sm:py-4 px-1 border-b-2 border-blue-500 font-medium text-xs sm:text-sm text-blue-600 whitespace-nowrap" data-tab="info">
                                <i class="fas fa-user mr-1 sm:mr-2 text-xs sm:text-sm"></i>
                                <span class="hidden sm:inline"><?= $__('profile.view.personal_info') ?></span>
                                <span class="sm:hidden">Info</span>
                            </button>
                            <button class="profile-tab py-3 sm:py-4 px-1 border-b-2 border-transparent font-medium text-xs sm:text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap" data-tab="verification">
                                <i class="fas fa-shield-check mr-1 sm:mr-2 text-xs sm:text-sm"></i>
                                <span class="hidden sm:inline"><?= $__('profile.view.account_verification') ?></span>
                                <span class="sm:hidden">Verify</span>
                            </button>
                            <button class="profile-tab py-3 sm:py-4 px-1 border-b-2 border-transparent font-medium text-xs sm:text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap" data-tab="security">
                                <i class="fas fa-shield-alt mr-1 sm:mr-2 text-xs sm:text-sm"></i>
                                <span class="hidden sm:inline"><?= $__('profile.view.security') ?></span>
                                <span class="sm:hidden">Security</span>
                            </button>
                        </nav>
                    </div>

                    <!-- Tab Content -->
                    <div class="p-4 sm:p-6">
                        <!-- Personal Info Tab -->
                        <div id="info-tab" class="tab-content">
                            <h3 class="text-base sm:text-lg font-semibold text-gray-900 mb-4 sm:mb-6"><?= $__('shared.profile_js.personal_info') ?></h3>

                            <div class="space-y-4 sm:space-y-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                                    <div>
                                        <label class="block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                                            <i class="fas fa-user mr-1.5 sm:mr-2 text-gray-400 text-xs sm:text-sm"></i>
                                            <?= $__('shared.profile_js.username') ?>
                                        </label>
                                        <div class="w-full px-3 sm:px-4 py-2.5 sm:py-3 bg-gray-100 border border-gray-300 rounded-lg text-gray-900 text-sm sm:text-base">
                                            <?= $this->escape($user['username']) ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Verification Tab -->
                        <div id="verification-tab" class="tab-content hidden">
                            <h3 class="text-base sm:text-lg font-semibold text-gray-900 mb-4 sm:mb-6"><?= $__('shared.profile_js.account_verification') ?></h3>

                            <div class="space-y-6 sm:space-y-8">
                                <!-- Email Verification -->
                                <div class="bg-gray-50 rounded-lg p-4 sm:p-6">
                                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 sm:mb-4">
                                        <div class="mb-3 sm:mb-0">
                                            <h4 class="text-sm sm:text-base font-semibold text-gray-900 flex items-center">
                                                <i class="fas fa-envelope text-blue-600 mr-1.5 sm:mr-2 text-sm sm:text-base"></i>
                                                <?= $__('shared.profile_js.email_verification') ?>
                                            </h4>
                                            <p class="text-xs sm:text-sm text-gray-600 mt-1"><?= $__('shared.profile_js.email_verification_desc') ?></p>
                                        </div>
                                        <?php if ($user['is_email_verified'] ?? false): ?>
                                            <span class="inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium bg-green-100 text-green-800 flex-shrink-0">
                                                <i class="fas fa-check-circle mr-1 sm:mr-2"></i>
                                                <?= $__('shared.profile_js.verified') ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium bg-red-100 text-red-800 flex-shrink-0">
                                                <i class="fas fa-times-circle mr-1 sm:mr-2"></i>
                                                <?= $__('shared.profile_js.not_verified') ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>

                                    <?php if (!($user['is_email_verified'] ?? false)): ?>
                                        <div class="space-y-3 sm:space-y-4">
                                            <div>
                                                <label class="block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2"><?= $__('shared.profile_js.email_address') ?></label>
                                                <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                                                    <input type="email" id="emailInput" value="<?= $this->escape($user['email'] ?? '') ?>" class="flex-1 px-3 sm:px-4 py-2.5 sm:py-3 bg-white border border-gray-300 rounded-lg text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm sm:text-base" placeholder="<?= $__('shared.profile_js.enter_email') ?>">
                                                    <button type="button" id="sendEmailVerificationBtn" class="px-4 sm:px-6 py-2.5 sm:py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium text-sm sm:text-base whitespace-nowrap">
                                                        <i class="fas fa-paper-plane mr-1 sm:mr-2"></i>
                                                        <span class="hidden sm:inline"><?= $__('shared.profile_js.send_verification_code') ?></span>
                                                        <span class="sm:hidden">Send</span>
                                                    </button>
                                                </div>
                                            </div>

                                            <div id="emailVerificationSection" class="hidden">
                                                <label class="block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2"><?= $__('shared.profile_js.verification_code') ?></label>
                                                <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                                                    <input type="text" id="emailVerificationCode" class="flex-1 px-3 sm:px-4 py-2.5 sm:py-3 border border-gray-300 rounded-lg text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base" placeholder="<?= $__('shared.profile_js.enter_verification_code') ?>">
                                                    <button type="button" id="verifyEmailBtn" class="px-4 sm:px-6 py-2.5 sm:py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium text-sm sm:text-base whitespace-nowrap">
                                                        <i class="fas fa-check mr-1 sm:mr-2"></i>
                                                        <?= $__('shared.profile_js.verify') ?>
                                                    </button>
                                                </div>
                                                <p class="text-xs text-gray-500 mt-2"><?= $__('shared.profile_js.check_email_instruction') ?></p>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <div class="flex items-center text-green-700 bg-green-50 p-4 rounded-lg">
                                            <i class="fas fa-check-circle mr-3 text-green-600"></i>
                                            <div>
                                                <p class="font-medium"><?= $__('shared.profile_js.email_verified_success') ?></p>
                                                <p class="text-sm text-green-600">Email: <?= $this->escape($user['email'] ?? '') ?></p>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Phone Verification -->
                                <div class="bg-gray-50 rounded-lg p-4 sm:p-6">
                                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 sm:mb-4">
                                        <div class="mb-3 sm:mb-0">
                                            <h4 class="text-sm sm:text-base font-semibold text-gray-900 flex items-center">
                                                <i class="fas fa-phone text-green-600 mr-1.5 sm:mr-2 text-sm sm:text-base"></i>
                                                <?= $__('shared.profile_js.phone_verification') ?>
                                            </h4>
                                            <p class="text-xs sm:text-sm text-gray-600 mt-1"><?= $__('shared.profile_js.phone_verification_desc') ?></p>
                                        </div>
                                        <?php if ($user['is_phone_verified'] ?? false): ?>
                                            <span class="inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium bg-green-100 text-green-800 flex-shrink-0">
                                                <i class="fas fa-check-circle mr-1 sm:mr-2"></i>
                                                <?= $__('shared.profile_js.verified') ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium bg-red-100 text-red-800 flex-shrink-0">
                                                <i class="fas fa-times-circle mr-1 sm:mr-2"></i>
                                                <?= $__('shared.profile_js.not_verified') ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>

                                    <?php if (!($user['is_phone_verified'] ?? false)): ?>
                                        <div class="space-y-3 sm:space-y-4">
                                            <div>
                                                <label class="block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2"><?= $__('shared.profile_js.phone_number') ?></label>
                                                <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                                                    <input type="tel" id="phoneInput" value="<?= $this->escape($user['phone'] ?? '') ?>" class="flex-1 px-3 sm:px-4 py-2.5 sm:py-3 bg-white border border-gray-300 rounded-lg text-gray-900 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors text-sm sm:text-base" placeholder="<?= $__('shared.profile_js.enter_phone') ?>">
                                                    <button type="button" id="sendPhoneVerificationBtn" class="px-4 sm:px-6 py-2.5 sm:py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium text-sm sm:text-base whitespace-nowrap">
                                                        <i class="fas fa-sms mr-1 sm:mr-2"></i>
                                                        <span class="hidden sm:inline"><?= $__('shared.profile_js.send_otp') ?></span>
                                                        <span class="sm:hidden">Send</span>
                                                    </button>
                                                </div>
                                            </div>

                                            <div id="phoneVerificationSection" class="hidden">
                                                <label class="block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2"><?= $__('shared.profile_js.otp_code') ?></label>
                                                <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                                                    <input type="text" id="phoneVerificationCode" class="flex-1 px-3 sm:px-4 py-2.5 sm:py-3 border border-gray-300 rounded-lg text-gray-900 focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm sm:text-base" placeholder="<?= $__('shared.profile_js.enter_otp') ?>">
                                                    <button type="button" id="verifyPhoneBtn" class="px-4 sm:px-6 py-2.5 sm:py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium text-sm sm:text-base whitespace-nowrap">
                                                        <i class="fas fa-check mr-1 sm:mr-2"></i>
                                                        <?= $__('shared.profile_js.verify') ?>
                                                    </button>
                                                </div>
                                                <p class="text-xs text-gray-500 mt-2"><?= $__('shared.profile_js.check_sms_instruction') ?></p>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <div class="flex items-center text-green-700 bg-green-50 p-4 rounded-lg">
                                            <i class="fas fa-check-circle mr-3 text-green-600"></i>
                                            <div>
                                                <p class="font-medium"><?= $__('shared.profile_js.phone_verified_success') ?></p>
                                                <p class="text-sm text-green-600"><?= $__('shared.profile_js.phone_number') ?>: <?= $this->escape($user['phone'] ?? '') ?></p>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Security Tab -->
                        <div id="security-tab" class="tab-content hidden">
                            <h3 class="text-base sm:text-lg font-semibold text-gray-900 mb-4 sm:mb-6"><?= $__('shared.profile_js.account_security') ?></h3>

                            <div class="space-y-4 sm:space-y-6">
                                <!-- Change Password -->
                                <div class="bg-gray-50 rounded-lg p-4 sm:p-6">
                                    <div class="flex items-start sm:items-center mb-3 sm:mb-4">
                                        <i class="fas fa-key text-blue-600 mr-2 sm:mr-3 text-lg sm:text-xl flex-shrink-0 mt-0.5 sm:mt-0"></i>
                                        <div>
                                            <h4 class="text-sm sm:text-base font-semibold text-gray-900"><?= $__('shared.profile_js.change_password') ?></h4>
                                            <p class="text-xs sm:text-sm text-gray-600"><?= $__('shared.profile_js.change_password_desc') ?></p>
                                        </div>
                                    </div>

                                    <form id="changePasswordForm" class="space-y-3 sm:space-y-4">
                                        <!-- CSRF Token -->
                                        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">

                                        <div>
                                            <label class="block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                                                <i class="fas fa-lock mr-1.5 sm:mr-2 text-gray-400 text-xs sm:text-sm"></i>
                                                <?= $__('shared.profile_js.current_password') ?>
                                            </label>
                                            <input type="password" name="current_password" class="w-full px-3 sm:px-4 py-2.5 sm:py-3 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm sm:text-base" placeholder="<?= $__('shared.profile_js.enter_current_password') ?>" required>
                                        </div>
                                        <div>
                                            <label class="block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                                                <i class="fas fa-key mr-1.5 sm:mr-2 text-gray-400 text-xs sm:text-sm"></i>
                                                <?= $__('shared.profile_js.new_password') ?>
                                            </label>
                                            <input type="password" name="new_password" class="w-full px-3 sm:px-4 py-2.5 sm:py-3 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm sm:text-base" placeholder="<?= $__('shared.profile_js.enter_new_password') ?>" required>
                                            <p class="text-xs text-gray-500 mt-1"><?= $__('shared.profile_js.password_length_requirement') ?></p>
                                        </div>
                                        <div>
                                            <label class="block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                                                <i class="fas fa-check-circle mr-1.5 sm:mr-2 text-gray-400 text-xs sm:text-sm"></i>
                                                <?= $__('shared.profile_js.confirm_new_password') ?>
                                            </label>
                                            <input type="password" name="confirm_password" class="w-full px-3 sm:px-4 py-2.5 sm:py-3 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm sm:text-base" placeholder="<?= $__('shared.profile_js.enter_confirm_password') ?>" required>
                                        </div>
                                        <div class="flex justify-end">
                                            <button type="submit" class="bg-gradient-to-r from-blue-500 to-emerald-600 text-white py-2.5 sm:py-3 px-4 sm:px-6 rounded-lg font-semibold hover:from-blue-600 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 shadow-lg text-sm sm:text-base">
                                                <i class="fas fa-save mr-1.5 sm:mr-2"></i>
                                                <span class="hidden sm:inline"><?= $__('shared.profile_js.change_password') ?></span>
                                                <span class="sm:hidden">Change</span>
                                            </button>
                                        </div>
                                    </form>
                                </div>

                                <!-- Security Info -->
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 sm:p-4">
                                    <div class="flex items-start">
                                        <i class="fas fa-shield-alt text-blue-600 mt-0.5 sm:mt-1 mr-2 sm:mr-3 text-sm sm:text-base flex-shrink-0"></i>
                                        <div>
                                            <h4 class="text-xs sm:text-sm font-medium text-blue-900 mb-1"><?= $__('shared.profile_js.security_info_title') ?></h4>
                                            <p class="text-xs sm:text-sm text-blue-700 leading-relaxed">
                                                <?= $__('shared.profile_js.security_info_desc') ?>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $this->endSection(); ?>

<?php $this->startSection('scripts'); ?>
<script>
// Translation variables for JavaScript
const translations = {
    enterCurrentPassword: '<?= $__('shared.profile_js.enter_current_password_error') ?>',
    enterNewPassword: '<?= $__('shared.profile_js.enter_new_password_error') ?>',
    passwordLength: '<?= $__('shared.profile_js.password_length_error') ?>',
    confirmPassword: '<?= $__('shared.profile_js.confirm_password_error') ?>',
    passwordMismatch: '<?= $__('shared.profile_js.password_mismatch') ?>',
    changingPassword: '<?= $__('shared.profile_js.changing_password') ?>',
    enterEmail: '<?= $__('shared.profile_js.enter_email_error') ?>',
    sending: '<?= $__('shared.profile_js.sending') ?>',
    resendIn: '<?= $__('shared.profile_js.resend_in') ?>',
    enterEmailAndCode: '<?= $__('shared.profile_js.enter_email_and_code') ?>',
    verifying: '<?= $__('shared.profile_js.verifying') ?>',
    enterPhone: '<?= $__('shared.profile_js.enter_phone_error') ?>',
    enterPhoneAndOtp: '<?= $__('shared.profile_js.enter_phone_and_otp') ?>',
    errorOccurred: '<?= $__('shared.profile_js.error_occurred') ?>'
};

$(document).ready(function() {
    // Tab functionality
    $('.profile-tab').click(function() {
        const tabName = $(this).data('tab');

        // Update tab appearance
        $('.profile-tab').removeClass('active border-blue-500 text-blue-600').addClass('border-transparent text-gray-500');
        $(this).addClass('active border-blue-500 text-blue-600').removeClass('border-transparent text-gray-500');

        // Show/hide content
        $('.tab-content').addClass('hidden');
        $(`#${tabName}-tab`).removeClass('hidden');
    });

    // Note: Profile form removed - personal info is now read-only

    // Change password form
    $('#changePasswordForm').submit(function(e) {
        e.preventDefault();

        const currentPassword = $('input[name="current_password"]').val();
        const newPassword = $('input[name="new_password"]').val();
        const confirmPassword = $('input[name="confirm_password"]').val();

        // Client-side validation
        if (!currentPassword) {
            GamingApp.showNotification(translations.enterCurrentPassword, 'error');
            $('input[name="current_password"]').focus().addClass('border-red-500');
            return;
        }

        if (!newPassword) {
            GamingApp.showNotification(translations.enterNewPassword, 'error');
            $('input[name="new_password"]').focus().addClass('border-red-500');
            return;
        }

        if (newPassword.length < 6 || newPassword.length > 30) {
            GamingApp.showNotification(translations.passwordLength, 'error');
            $('input[name="new_password"]').focus().addClass('border-red-500');
            return;
        }

        if (!confirmPassword) {
            GamingApp.showNotification(translations.confirmPassword, 'error');
            $('input[name="confirm_password"]').focus().addClass('border-red-500');
            return;
        }

        if (newPassword !== confirmPassword) {
            GamingApp.showNotification(translations.passwordMismatch, 'error');
            $('input[name="confirm_password"]').focus().addClass('border-red-500');
            return;
        }

        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();

        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>' + translations.changingPassword);

        $.ajax({
            url: '<?= $this->url('/profile/change-password') ?>',
            method: 'POST',
            data: {
                current_password: currentPassword,
                new_password: newPassword,
                confirm_password: confirmPassword,
                csrf_token: $('input[name="csrf_token"]').val()
            },
            dataType: 'json',
            success: function(response) {
                if (response.status) {
                    GamingApp.showNotification(response.message, 'success');
                    // Clear form
                    $('#changePasswordForm')[0].reset();
                    // Remove any error styling
                    $('#changePasswordForm input').removeClass('border-red-500');

                    // Redirect to login page after password change for security
                    if (response.redirect) {
                        setTimeout(() => {
                            window.location.href = response.redirect;
                        }, 2000);
                    } else {
                        // Fallback: reload page
                        setTimeout(() => {
                            location.reload();
                        }, 2000);
                    }
                } else {
                    GamingApp.showNotification(response.message, 'error');
                    submitBtn.prop('disabled', false).html(originalText);
                }
            },
            error: function(xhr) {
                let errorMessage = translations.errorOccurred;

                try {
                    const response = xhr.responseJSON;
                    if (response && response.message) {
                        errorMessage = response.message;
                    }
                    if (response && response.errors) {
                        Object.keys(response.errors).forEach(field => {
                            const input = $(`input[name="${field}"]`);
                            input.addClass('border-red-500');
                            GamingApp.showNotification(response.errors[field][0], 'error');
                        });
                        submitBtn.prop('disabled', false).html(originalText);
                        return;
                    }
                } catch (e) {
                    console.error('Error parsing response:', e);
                }

                GamingApp.showNotification(errorMessage, 'error');
                submitBtn.prop('disabled', false).html(originalText);
            }
        });
    });

    // Clear error styling on input focus
    $('input').focus(function() {
        $(this).removeClass('border-red-500');
    });

    // Email verification functionality
    $('#sendEmailVerificationBtn').click(function() {
        const email = $('#emailInput').val();

        if (!email) {
            GamingApp.showNotification(translations.enterEmail, 'error');
            return;
        }

        const btn = $(this);
        const originalText = btn.html();

        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i>' + translations.sending);

        $.ajax({
            url: '<?= $this->url('/profile/send-email-verification') ?>',
            method: 'POST',
            data: {
                email: email,
                csrf_token: $('input[name="csrf_token"]').val()
            },
            dataType: 'json',
            success: function(response) {
                if (response.status) {
                    GamingApp.showNotification(response.message, 'success');
                    $('#emailVerificationSection').removeClass('hidden');

                    // Start countdown timer
                    let countdown = response.data?.wait_time || 60;
                    const countdownInterval = setInterval(() => {
                        btn.html(`<i class="fas fa-clock mr-1"></i>${translations.resendIn} (${countdown}s)`);
                        countdown--;

                        if (countdown < 0) {
                            clearInterval(countdownInterval);
                            btn.prop('disabled', false).html(originalText);
                        }
                    }, 1000);
                } else {
                    GamingApp.showNotification(response.message, 'error');
                    btn.prop('disabled', false).html(originalText);
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                GamingApp.showNotification(response?.message || translations.errorOccurred, 'error');
                btn.prop('disabled', false).html(originalText);
            }
        });
    });

    // Email verification code submission
    $('#verifyEmailBtn').click(function() {
        const email = $('#emailInput').val();
        const code = $('#emailVerificationCode').val();

        if (!email || !code) {
            GamingApp.showNotification(translations.enterEmailAndCode, 'error');
            return;
        }

        const btn = $(this);
        const originalText = btn.html();

        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i>' + translations.verifying);

        $.ajax({
            url: '<?= $this->url('/profile/verify-email') ?>',
            method: 'POST',
            data: {
                email: email,
                verification_code: code,
                csrf_token: $('input[name="csrf_token"]').val()
            },
            dataType: 'json',
            success: function(response) {
                if (response.status) {
                    GamingApp.showNotification(response.message, 'success');
                    // Reload page to update verification status
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    GamingApp.showNotification(response.message, 'error');
                    btn.prop('disabled', false).html(originalText);
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                GamingApp.showNotification(response?.message || translations.errorOccurred, 'error');
                btn.prop('disabled', false).html(originalText);
            }
        });
    });

    // Phone verification functionality
    $('#sendPhoneVerificationBtn').click(function() {
        const phone = $('#phoneInput').val();

        if (!phone) {
            GamingApp.showNotification(translations.enterPhone, 'error');
            return;
        }

        const btn = $(this);
        const originalText = btn.html();

        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i>' + translations.sending);

        $.ajax({
            url: '<?= $this->url('/profile/send-phone-verification') ?>',
            method: 'POST',
            data: {
                phone: phone,
                csrf_token: $('input[name="csrf_token"]').val()
            },
            dataType: 'json',
            success: function(response) {
                if (response.status) {
                    GamingApp.showNotification(response.message, 'success');
                    $('#phoneVerificationSection').removeClass('hidden');

                    // Start countdown timer
                    let countdown = response.data?.wait_time || 60;
                    const countdownInterval = setInterval(() => {
                        btn.html(`<i class="fas fa-clock mr-1"></i>${translations.resendIn} (${countdown}s)`);
                        countdown--;

                        if (countdown < 0) {
                            clearInterval(countdownInterval);
                            btn.prop('disabled', false).html(originalText);
                        }
                    }, 1000);
                } else {
                    GamingApp.showNotification(response.message, 'error');
                    btn.prop('disabled', false).html(originalText);
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                GamingApp.showNotification(response?.message || translations.errorOccurred, 'error');
                btn.prop('disabled', false).html(originalText);
            }
        });
    });

    // Phone verification code submission
    $('#verifyPhoneBtn').click(function() {
        const phone = $('#phoneInput').val();
        const code = $('#phoneVerificationCode').val();

        if (!phone || !code) {
            GamingApp.showNotification(translations.enterPhoneAndOtp, 'error');
            return;
        }

        const btn = $(this);
        const originalText = btn.html();

        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i>' + translations.verifying);

        $.ajax({
            url: '<?= $this->url('/profile/verify-phone') ?>',
            method: 'POST',
            data: {
                phone: phone,
                otp_code: code,
                csrf_token: $('input[name="csrf_token"]').val()
            },
            dataType: 'json',
            success: function(response) {
                if (response.status) {
                    GamingApp.showNotification(response.message, 'success');
                    // Reload page to update verification status
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    GamingApp.showNotification(response.message, 'error');
                    btn.prop('disabled', false).html(originalText);
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                GamingApp.showNotification(response?.message || translations.errorOccurred, 'error');
                btn.prop('disabled', false).html(originalText);
            }
        });
    });
});
</script>
<?php $this->endSection(); ?>

<!-- Include the shared layout -->
<?php include dirname(dirname(dirname(dirname(__DIR__)))) . '/shared/Views/layouts/app.php'; ?>
