@echo off
echo ========================================
echo    PHP Error Logs Viewer
echo ========================================
echo.

if "%1"=="php" goto php_logs
if "%1"=="nginx" goto nginx_logs
if "%1"=="all" goto all_logs

:menu
echo Choose log type to view:
echo 1. PHP Error Logs
echo 2. Nginx Error Logs  
echo 3. All Logs
echo 4. Follow PHP Logs (real-time)
echo.
set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" goto php_logs
if "%choice%"=="2" goto nginx_logs
if "%choice%"=="3" goto all_logs
if "%choice%"=="4" goto follow_logs
goto menu

:php_logs
echo.
echo === PHP Error Logs ===
if exist "error_log\php_errors.log" (
    type "error_log\php_errors.log"
) else (
    echo No PHP error logs found. Make sure Docker is running and generating logs.
)
goto end

:nginx_logs
echo.
echo === Nginx Error Logs ===
if exist "error_log\error.log" (
    type "error_log\error.log"
) else (
    echo No Nginx error logs found.
)
goto end

:all_logs
echo.
echo === All Error Logs ===
echo.
echo --- PHP Errors ---
if exist "error_log\php_errors.log" (
    type "error_log\php_errors.log"
) else (
    echo No PHP error logs found.
)
echo.
echo --- Nginx Errors ---
if exist "error_log\error.log" (
    type "error_log\error.log"
) else (
    echo No Nginx error logs found.
)
goto end

:follow_logs
echo.
echo Following PHP error logs (Ctrl+C to stop)...
echo.
powershell -Command "Get-Content 'error_log\php_errors.log' -Wait -Tail 10"
goto end

:end
echo.
echo ========================================
pause
