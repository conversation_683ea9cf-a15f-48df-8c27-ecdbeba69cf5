<?php

namespace Shared\Services;

use Core\Model;
use Core\Application;

/**
 * API Service
 * Handles all API communications with the backend
 */
class ApiService extends Model
{
    /**
     * Register new user account
     */
    public function register(array $data): array
    {
        $this->validateRequired($data, ['register_type', 'source']);

        if ($data['register_type'] === 'account') {
            $this->validateRequired($data, ['username', 'password']);
        }

        $endpoint = Application::getInstance()->getConfig('api.endpoints.register');
        $response = $this->post($endpoint, $data);

        return $this->handleResponse($response);
    }

    /**
     * Login user
     */
    public function login(array $data): array
    {
        $this->validateRequired($data, ['login_type']);

        if ($data['login_type'] === 'account') {
            $this->validateRequired($data, ['username', 'password']);
        } elseif (in_array($data['login_type'], ['google', 'facebook', 'apple'])) {
            $this->validateRequired($data, ['id_token']);
        }


        $endpoint = Application::getInstance()->getConfig('api.endpoints.login');
        $response = $this->post($endpoint, $data);


        return $this->handleResponse($response);
    }

    /**
     * Verify access token
     */
    public function verifyToken(string $token): array
    {
        $this->setAuthToken($token);

        $endpoint = Application::getInstance()->getConfig('api.endpoints.verify_token');
        $response = $this->get($endpoint);

        return $this->handleResponse($response);
    }

    /**
     * Send forgot password request
     */
    public function forgotPassword(string $email): array
    {
        $data = ['email' => $email];

        $endpoint = Application::getInstance()->getConfig('api.endpoints.forgot_password');
        $response = $this->post($endpoint, $data);

        return $this->handleResponse($response);
    }

    /**
     * Reset password with token
     */
    public function resetPassword(string $token, string $newPassword, string $confirmPassword): array
    {
        $data = [
            'token' => $token,
            'new_password' => $newPassword,
            'confirm_password' => $confirmPassword
        ];

        $endpoint = Application::getInstance()->getConfig('api.endpoints.reset_password');
        $response = $this->post($endpoint, $data);

        return $this->handleResponse($response);
    }

    /**
     * Send phone verification OTP
     */
    public function sendPhoneVerification(string $phone, string $token): array
    {
        $this->setAuthToken($token);

        $data = ['phone' => $phone];

        $endpoint = Application::getInstance()->getConfig('api.endpoints.send_phone_verification');
        $response = $this->post($endpoint, $data);

        return $this->handleResponse($response);
    }

    /**
     * Verify phone OTP
     */
    public function verifyPhoneOTP(string $phone, string $otp, string $token): array
    {
        $this->setAuthToken($token);

        $data = [
            'phone' => $phone,
            'otp' => $otp
        ];

        $endpoint = Application::getInstance()->getConfig('api.endpoints.verify_phone_otp');
        $response = $this->post($endpoint, $data);

        return $this->handleResponse($response);
    }

    /**
     * Get app information
     */
    public function getAppInfo(string $source = 'web'): array
    {
        $app = Application::getInstance();

        $data = [
            'app_id' => $app->getConfig('api.app_id'),
            'app_secret' => $app->getConfig('api.app_secret'),
            'source' => $source
        ];

        $endpoint = Application::getInstance()->getConfig('api.endpoints.app_info');
        $response = $this->post($endpoint, $data);

        return $this->handleResponse($response);
    }

    /**
     * Get user balance (using verify token endpoint)
     */
    public function getUserBalance(string $token): array
    {
        // Use verify token endpoint which returns user data including balance
        return $this->verifyToken($token);
    }

    /**
     * Send email verification code
     */
    public function sendEmailVerification(string $email, string $token): array
    {
        $this->setAuthToken($token);

        $endpoint = Application::getInstance()->getConfig('api.endpoints.send_email_verification');
        $response = $this->post($endpoint, [
            'email' => $email
        ]);

        return $this->handleResponse($response);
    }

    /**
     * Verify email with code
     */
    public function verifyEmail(string $email, string $verificationCode, string $token): array
    {
        $this->setAuthToken($token);

        $endpoint = Application::getInstance()->getConfig('api.endpoints.verify_email');
        $response = $this->post($endpoint, [
            'email' => $email,
            'verification_code' => $verificationCode
        ]);

        return $this->handleResponse($response);
    }

    /**
     * Change password
     */
    public function changePassword(string $currentPassword, string $newPassword, string $confirmPassword, string $token): array
    {
        $this->setAuthToken($token);

        $endpoint = Application::getInstance()->getConfig('api.endpoints.change_password');
        $response = $this->post($endpoint, [
            'current_password' => $currentPassword,
            'new_password' => $newPassword,
            'confirm_password' => $confirmPassword
        ]);

        return $this->handleResponse($response);
    }

    /**
     * Validate user input for registration
     */
    public function validateRegistrationData(array $data): array
    {
        $errors = [];

        // Validate username
        if (isset($data['username'])) {
            $username = $data['username'];
            if (strlen($username) < 6 || strlen($username) > 20) {
                $errors['username'] = 'Tên đăng nhập phải từ 6-20 ký tự';
            } elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
                $errors['username'] = 'Tên đăng nhập chỉ được chứa chữ cái, số và dấu gạch dưới';
            }
        }

        // Validate password
        if (isset($data['password'])) {
            $password = $data['password'];
            if (strlen($password) < 6 || strlen($password) > 30) {
                $errors['password'] = 'Mật khẩu phải từ 6-30 ký tự';
            }
        }

        // Validate email
        if (isset($data['email']) && !empty($data['email'])) {
            if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                $errors['email'] = 'Email không hợp lệ';
            }
        }

        // Validate phone
        if (isset($data['phone']) && !empty($data['phone'])) {
            if (!preg_match('/^0[0-9]{9}$/', $data['phone'])) {
                $errors['phone'] = 'Số điện thoại phải có 10 chữ số và bắt đầu bằng số 0';
            }
        }

        return $errors;
    }

    /**
     * Validate login data
     */
    public function validateLoginData(array $data): array
    {
        $errors = [];

        if ($data['login_type'] === 'account') {
            if (empty($data['username'])) {
                $errors['username'] = 'Vui lòng nhập tên đăng nhập';
            }

            if (empty($data['password'])) {
                $errors['password'] = 'Vui lòng nhập mật khẩu';
            }
        }

        return $errors;
    }
}
