[30-May-2025 15:17:51 Asia/<PERSON>_<PERSON>] API Call: {"timestamp":"2025-05-30 15:17:51","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"guest_683966b81dc06","username":"huynhty","email":null,"is_email_verified":false,"phone":null,"is_phone_verified":false,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:17:40"}}},"http_code":200}
[30-May-2025 15:17:51 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"guest_683966b81dc06","username":"huynhty","email":null,"is_email_verified":false,"phone":null,"is_phone_verified":false,"user_type":"account","balance":0}
[30-May-2025 15:18:13 Asia/<PERSON>_<PERSON>_<PERSON>] URL: http://192.168.1.4:81/api/GameUser/SendEmailVerification - Data: {"email":"<EMAIL>"}headers: ["Content-Type: application\/json","Accept: application\/json","User-Agent: AFKSDK\/1.0.0 (Web Client; Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36)","Authorization: Bearer b66a5c22454c9c7608cfcb5c3c70acd532269e3fce92acaf098017be4951318d"]
[30-May-2025 15:18:13 Asia/Ho_Chi_Minh] Invalid JSON response: array(3) {
  ["allowed"]=>
  bool(true)
  ["remaining"]=>
  int(0)
  ["reset_time"]=>
  int(**********)
}

[30-May-2025 15:18:18 Asia/Ho_Chi_Minh] URL: http://192.168.1.4:81/api/GameUser/SendEmailVerification - Data: {"email":"<EMAIL>"}headers: ["Content-Type: application\/json","Accept: application\/json","User-Agent: AFKSDK\/1.0.0 (Web Client; Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36)","Authorization: Bearer b66a5c22454c9c7608cfcb5c3c70acd532269e3fce92acaf098017be4951318d"]
[30-May-2025 15:18:18 Asia/Ho_Chi_Minh] Invalid JSON response: array(3) {
  ["allowed"]=>
  bool(false)
  ["remaining"]=>
  int(0)
  ["reset_time"]=>
  int(1748593158)
}

[30-May-2025 15:18:36 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:18:36","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":true,"message":"Mã xác thực đã được gửi","data":{"expires_at":"2025-05-30 15:23:36","wait_time":60}},"http_code":200}
[30-May-2025 15:18:49 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:18:49","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"guest_683966b81dc06","username":"huynhty","email":null,"is_email_verified":false,"phone":null,"is_phone_verified":false,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:17:40"}}},"http_code":200}
[30-May-2025 15:18:49 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"guest_683966b81dc06","username":"huynhty","email":null,"is_email_verified":false,"phone":null,"is_phone_verified":false,"user_type":"account","balance":0}
[30-May-2025 15:18:51 Asia/Ho_Chi_Minh] URL: http://192.168.1.4:81/api/GameUser/SendEmailVerification - Data: {"email":"<EMAIL>"}headers: ["Content-Type: application\/json","Accept: application\/json","User-Agent: AFKSDK\/1.0.0 (Web Client; Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36)","Authorization: Bearer b66a5c22454c9c7608cfcb5c3c70acd532269e3fce92acaf098017be4951318d"]
[30-May-2025 15:18:51 Asia/Ho_Chi_Minh] Invalid JSON response: array(3) {
  ["allowed"]=>
  bool(false)
  ["remaining"]=>
  int(0)
  ["reset_time"]=>
  int(**********)
}

[30-May-2025 15:19:18 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:19:18","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"guest_683966b81dc06","username":"huynhty","email":null,"is_email_verified":false,"phone":null,"is_phone_verified":false,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:17:40"}}},"http_code":200}
[30-May-2025 15:19:18 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"guest_683966b81dc06","username":"huynhty","email":null,"is_email_verified":false,"phone":null,"is_phone_verified":false,"user_type":"account","balance":0}
[30-May-2025 15:19:23 Asia/Ho_Chi_Minh] URL: http://192.168.1.4:81/api/GameUser/SendEmailVerification - Data: {"email":"<EMAIL>"}headers: ["Content-Type: application\/json","Accept: application\/json","User-Agent: AFKSDK\/1.0.0 (Web Client; Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36)","Authorization: Bearer b66a5c22454c9c7608cfcb5c3c70acd532269e3fce92acaf098017be4951318d"]
[30-May-2025 15:19:23 Asia/Ho_Chi_Minh] Invalid JSON response: array(3) {
  ["allowed"]=>
  bool(false)
  ["remaining"]=>
  int(0)
  ["reset_time"]=>
  int(**********)
}
bool(true)

[30-May-2025 15:20:28 Asia/Ho_Chi_Minh] URL: http://192.168.1.4:81/api/GameUser/SendEmailVerification - Data: {"email":"<EMAIL>"}headers: ["Content-Type: application\/json","Accept: application\/json","User-Agent: AFKSDK\/1.0.0 (Web Client; Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36)","Authorization: Bearer b66a5c22454c9c7608cfcb5c3c70acd532269e3fce92acaf098017be4951318d"]
[30-May-2025 15:20:28 Asia/Ho_Chi_Minh] Invalid JSON response: array(3) {
  ["allowed"]=>
  bool(true)
  ["remaining"]=>
  int(0)
  ["reset_time"]=>
  int(1748593288)
}
bool(false)

[30-May-2025 15:20:55 Asia/Ho_Chi_Minh] URL: http://192.168.1.4:81/api/GameUser/SendEmailVerification - Data: {"email":"<EMAIL>"}headers: ["Content-Type: application\/json","Accept: application\/json","User-Agent: AFKSDK\/1.0.0 (Web Client; Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36)","Authorization: Bearer b66a5c22454c9c7608cfcb5c3c70acd532269e3fce92acaf098017be4951318d"]
[30-May-2025 15:20:55 Asia/Ho_Chi_Minh] Invalid JSON response: array(3) {
  ["allowed"]=>
  bool(false)
  ["remaining"]=>
  int(0)
  ["reset_time"]=>
  int(1748593315)
}
bool(true)
{"status":true,"message":"Mã xác thực đã được gửi","data":{"expires_at":"2025-05-30 15:25:55","wait_time":60}}
[30-May-2025 15:21:37 Asia/Ho_Chi_Minh] URL: http://192.168.1.4:81/api/GameUser/SendEmailVerification - Data: {"email":"<EMAIL>"}headers: ["Content-Type: application\/json","Accept: application\/json","User-Agent: AFKSDK\/1.0.0 (Web Client; Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36)","Authorization: Bearer b66a5c22454c9c7608cfcb5c3c70acd532269e3fce92acaf098017be4951318d"]
[30-May-2025 15:21:37 Asia/Ho_Chi_Minh] Invalid JSON response: array(3) {
  ["allowed"]=>
  bool(false)
  ["remaining"]=>
  int(0)
  ["reset_time"]=>
  int(1748593357)
}
bool(true)
{"status":true,"message":"Mã xác thực đã được gửi","data":{"expires_at":"2025-05-30 15:26:37","wait_time":60}}
[30-May-2025 15:22:56 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:22:56","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"guest_683966b81dc06","username":"huynhty","email":null,"is_email_verified":false,"phone":null,"is_phone_verified":false,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:17:40"}}},"http_code":200}
[30-May-2025 15:22:56 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"guest_683966b81dc06","username":"huynhty","email":null,"is_email_verified":false,"phone":null,"is_phone_verified":false,"user_type":"account","balance":0}
[30-May-2025 15:23:02 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:23:02","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":true,"message":"Mã xác thực đã được gửi","data":{"expires_at":"2025-05-30 15:28:02","wait_time":60}},"http_code":200}
[30-May-2025 15:23:05 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:23:05","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"guest_683966b81dc06","username":"huynhty","email":null,"is_email_verified":false,"phone":null,"is_phone_verified":false,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:17:40"}}},"http_code":200}
[30-May-2025 15:23:05 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"guest_683966b81dc06","username":"huynhty","email":null,"is_email_verified":false,"phone":null,"is_phone_verified":false,"user_type":"account","balance":0}
[30-May-2025 15:23:06 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:23:06","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Rate limit exceeded. Tối đa 1 request\/phút cho endpoint này. Còn lại 0 requests","rate_limit":{"limit":1,"remaining":0,"reset_time":**********}},"http_code":429}
[30-May-2025 15:23:11 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:23:11","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Rate limit exceeded. Tối đa 1 request\/phút cho endpoint này. Còn lại 0 requests","rate_limit":{"limit":1,"remaining":0,"reset_time":**********}},"http_code":429}
[30-May-2025 15:23:13 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:23:13","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"guest_683966b81dc06","username":"huynhty","email":null,"is_email_verified":false,"phone":null,"is_phone_verified":false,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:17:40"}}},"http_code":200}
[30-May-2025 15:23:13 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"guest_683966b81dc06","username":"huynhty","email":null,"is_email_verified":false,"phone":null,"is_phone_verified":false,"user_type":"account","balance":0}
[30-May-2025 15:23:15 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:23:15","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Rate limit exceeded. Tối đa 1 request\/phút cho endpoint này. Còn lại 0 requests","rate_limit":{"limit":1,"remaining":0,"reset_time":**********}},"http_code":429}
[30-May-2025 15:23:21 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:23:21","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Rate limit exceeded. Tối đa 1 request\/phút cho endpoint này. Còn lại 0 requests","rate_limit":{"limit":1,"remaining":0,"reset_time":**********}},"http_code":429}
[30-May-2025 15:24:10 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:24:10","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau","rate_limit":{"limit":1,"remaining":0,"reset_time":1748593510}},"http_code":429}
[30-May-2025 15:24:11 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:24:11","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau","rate_limit":{"limit":1,"remaining":0,"reset_time":1748593511}},"http_code":429}
[30-May-2025 15:24:18 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:24:18","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau","rate_limit":{"limit":1,"remaining":0,"reset_time":1748593518}},"http_code":429}
[30-May-2025 15:24:32 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:24:32","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 1748593532 giây","rate_limit":{"limit":1,"remaining":0,"reset_time":1748593532}},"http_code":429}
[30-May-2025 15:24:54 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:24:54","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 1748593554 giây","rate_limit":{"limit":1,"remaining":0,"reset_time":1748593554}},"http_code":429}
[30-May-2025 15:24:59 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:24:59","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:25:59"},"http_code":429}
[30-May-2025 15:25:49 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:25:49","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:26:49"},"http_code":429}
[30-May-2025 15:25:56 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:25:56","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:26:56"},"http_code":429}
[30-May-2025 15:28:03 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:03","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":true,"message":"Mã xác thực đã được gửi","data":{"expires_at":"2025-05-30 15:33:03","wait_time":60}},"http_code":200}
[30-May-2025 15:28:07 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:07","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"guest_683966b81dc06","username":"huynhty","email":null,"is_email_verified":false,"phone":null,"is_phone_verified":false,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:17:40"}}},"http_code":200}
[30-May-2025 15:28:07 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"guest_683966b81dc06","username":"huynhty","email":null,"is_email_verified":false,"phone":null,"is_phone_verified":false,"user_type":"account","balance":0}
[30-May-2025 15:28:09 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:09","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:10 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:10","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:11 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:11","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:12 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:11","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:12 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:12","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:12 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:12","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:12 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:12","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:13 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:13","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:14 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:14","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:14 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:14","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:14 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:14","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:15 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:15","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:15 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:15","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:15 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:15","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:15 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:15","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:16 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:16","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:16 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:16","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:20 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:20","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:20 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:20","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:21 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:21","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:21 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:21","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:21 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:21","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:21 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:21","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:22 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:22","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:22 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:22","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:25 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:25","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:25 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:25","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:25 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:25","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:25 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:25","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:26 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:26","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:26 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:26","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:26 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:26","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:26 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:26","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:26 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:26","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:27 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:27","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:27 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:27","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:27 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:27","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:27 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:27","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:28 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:28","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:28 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:28","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:28 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:28","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:28 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:28","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:28 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:28","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:29 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:29","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:29 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:29","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:29 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:29","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:29 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:29","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:29 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:29","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:30 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:30","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:30 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:30","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:30 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:30","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:30 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:30","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:30 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:30","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:31 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:31","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:31 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:31","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:31 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:31","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:31 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:31","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:32 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:32","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:32 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:32","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:32 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:32","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:32 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:32","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:32 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:32","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:33 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:33","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:33 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:33","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:33 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:33","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:33 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:33","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:28:34 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:28:34","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau 2025-05-30 15:29:03"},"http_code":429}
[30-May-2025 15:29:28 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:29:28","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":true,"message":"Mã xác thực đã được gửi","data":{"expires_at":"2025-05-30 15:34:28","wait_time":60}},"http_code":200}
[30-May-2025 15:36:52 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:36:52","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyEmail","request_data":{"email":"<EMAIL>","verification_code":"372797"},"response":{"status":false,"message":"Mã xác thực không hợp lệ hoặc đã hết hạn"},"http_code":200}
[30-May-2025 15:37:06 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:37:06","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":true,"message":"Mã xác thực đã được gửi","data":{"expires_at":"2025-05-30 15:42:06","wait_time":60}},"http_code":200}
[30-May-2025 15:37:13 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:37:13","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyEmail","request_data":{"email":"<EMAIL>","verification_code":"234431"},"response":{"status":true,"message":"Xác thực email thành công","data":{"user":{"user_id":"guest_683966b81dc06","username":"huynhty","email":"<EMAIL>","is_email_verified":true,"phone":null,"is_phone_verified":false,"user_type":"account","balance":0}}},"http_code":200}
[30-May-2025 15:37:14 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:37:14","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"guest_683966b81dc06","username":"huynhty","email":"<EMAIL>","is_email_verified":true,"phone":null,"is_phone_verified":false,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:17:40"}}},"http_code":200}
[30-May-2025 15:37:14 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"guest_683966b81dc06","username":"huynhty","email":"<EMAIL>","is_email_verified":true,"phone":null,"is_phone_verified":false,"user_type":"account","balance":0}
[30-May-2025 15:37:39 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:37:39","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"guest_683966b81dc06","username":"huynhty","email":"<EMAIL>","is_email_verified":true,"phone":null,"is_phone_verified":false,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:17:40"}}},"http_code":200}
[30-May-2025 15:37:39 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"guest_683966b81dc06","username":"huynhty","email":"<EMAIL>","is_email_verified":true,"phone":null,"is_phone_verified":false,"user_type":"account","balance":0}
[30-May-2025 15:48:14 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:48:14","method":"POST","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":true,"message":"Login successful","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"access_token":"47b272b6dd01d926b86212ecb47e6fc5999bc513d21b41f669e71bd9878ad689","refresh_token":"1a4c41b475a910d38ada252c575d0964a29b178272ea234aea7ec9eea78e9928","expires_at":"2025-06-06 15:48:14"}}},"http_code":200}
[30-May-2025 15:48:16 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:48:16","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:48:14"}}},"http_code":200}
[30-May-2025 15:48:16 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 15:48:22 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:48:22","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:48:14"}}},"http_code":200}
[30-May-2025 15:48:22 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 15:48:22 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:48:22","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:48:14"}}},"http_code":200}
[30-May-2025 15:48:32 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:48:32","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:48:14"}}},"http_code":200}
[30-May-2025 15:48:32 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 15:48:35 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:48:35","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:48:14"}}},"http_code":200}
[30-May-2025 15:48:35 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 15:49:16 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:49:16","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:48:14"}}},"http_code":200}
[30-May-2025 15:49:16 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 15:49:18 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:49:18","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:48:14"}}},"http_code":200}
[30-May-2025 15:49:18 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 15:49:19 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:49:19","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:48:14"}}},"http_code":200}
[30-May-2025 15:49:29 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:49:29","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:48:14"}}},"http_code":200}
[30-May-2025 15:49:29 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 15:49:32 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:49:32","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:48:14"}}},"http_code":200}
[30-May-2025 15:49:32 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 15:50:43 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:50:43","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:48:14"}}},"http_code":200}
[30-May-2025 15:50:43 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 15:52:46 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:52:46","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:48:14"}}},"http_code":200}
[30-May-2025 15:52:46 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 15:52:51 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:52:51","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:48:14"}}},"http_code":200}
[30-May-2025 15:52:51 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 15:53:00 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:53:00","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:48:14"}}},"http_code":200}
[30-May-2025 15:53:00 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 15:56:12 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:56:12","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:48:14"}}},"http_code":200}
[30-May-2025 15:56:12 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 15:56:42 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:56:42","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:48:14"}}},"http_code":200}
[30-May-2025 15:56:42 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 15:57:00 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:57:00","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:48:14"}}},"http_code":200}
[30-May-2025 15:57:00 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 15:57:23 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:57:23","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:48:14"}}},"http_code":200}
[30-May-2025 15:57:23 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 15:57:30 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:57:30","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:48:14"}}},"http_code":200}
[30-May-2025 15:57:30 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 15:58:51 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 15:58:51","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:48:14"}}},"http_code":200}
[30-May-2025 15:58:51 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 16:00:40 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 16:00:40","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:48:14"}}},"http_code":200}
[30-May-2025 16:00:40 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 16:01:17 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 16:01:17","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:48:14"}}},"http_code":200}
[30-May-2025 16:01:17 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 16:01:21 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 16:01:21","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:48:14"}}},"http_code":200}
[30-May-2025 16:01:21 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 16:02:49 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 16:02:49","method":"GET","url":"http:\/\/192.168.1.4:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 15:48:14"}}},"http_code":200}
[30-May-2025 16:02:49 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
