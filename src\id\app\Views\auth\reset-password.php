<?php $this->startSection('content'); ?>

<div class="auth-container px-4 sm:px-6 lg:px-8 py-4 sm:py-6">
    <div class="w-full max-w-md">
        <!-- Header -->
        <div class="text-center mb-6 sm:mb-8">
            <div class="w-12 h-12 sm:w-16 sm:h-16 bg-primary-500 rounded-xl flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-lg">
                <i class="fas fa-lock text-white text-lg sm:text-xl"></i>
            </div>
            <h1 class="text-2xl sm:text-3xl font-gaming font-bold text-gray-900 mb-2"><?= $__('auth.reset_password.view.title') ?></h1>
            <p class="text-gray-600 text-sm sm:text-base"><?= $__('auth.reset_password.view.subtitle') ?></p>
        </div>

        <div class="auth-form-container rounded-xl p-4 sm:p-6 lg:p-8">
            <!-- Reset Password Form -->
            <form id="resetPasswordForm" class="space-y-4 sm:space-y-6">
                <!-- CSRF Token -->
                <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">
                <!-- Reset Token -->
                <input type="hidden" name="token" value="<?= htmlspecialchars($reset_token) ?>">

                <!-- New Password -->
                <div class="relative">
                    <label class="block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                        <i class="fas fa-lock mr-1.5 sm:mr-2 text-gray-400 text-xs sm:text-sm"></i>
                        <?= $__('auth.reset_password.view.new_password') ?>
                    </label>
                    <div class="relative">
                        <input type="password" name="new_password" class="w-full px-3 sm:px-4 py-2.5 sm:py-3 pr-10 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-sm sm:text-base" placeholder="<?= $__('auth.reset_password.view.enter_new_password') ?>" required minlength="6" maxlength="30">
                        <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center toggle-password" data-target="new_password">
                            <i class="fas fa-eye text-gray-400 hover:text-gray-600 transition-colors"></i>
                        </button>
                    </div>
                    <p class="text-xs text-gray-500 mt-1"><?= $__('auth.reset_password.view.password_requirements') ?></p>
                </div>

                <!-- Confirm Password -->
                <div class="relative">
                    <label class="block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                        <i class="fas fa-lock mr-1.5 sm:mr-2 text-gray-400 text-xs sm:text-sm"></i>
                        <?= $__('auth.reset_password.view.confirm_password') ?>
                    </label>
                    <div class="relative">
                        <input type="password" name="confirm_password" class="w-full px-3 sm:px-4 py-2.5 sm:py-3 pr-10 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-sm sm:text-base" placeholder="<?= $__('auth.reset_password.view.enter_confirm_password') ?>" required minlength="6" maxlength="30">
                        <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center toggle-password" data-target="confirm_password">
                            <i class="fas fa-eye text-gray-400 hover:text-gray-600 transition-colors"></i>
                        </button>
                    </div>
                </div>

                <!-- Password Strength Indicator -->
                <div id="passwordStrength" class="hidden">
                    <div class="flex items-center space-x-2 mb-2">
                        <span class="text-xs text-gray-600"><?= $__('shared.password_strength.strength') ?>:</span>
                        <span id="strengthText" class="text-xs font-medium"></span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div id="strengthBar" class="h-2 rounded-full transition-all duration-300"></div>
                    </div>
                </div>

                <button type="submit" class="w-full bg-gradient-to-r from-emerald-500 to-blue-600 text-white py-2.5 sm:py-3 px-4 rounded-lg font-gaming font-semibold hover:from-emerald-600 hover:to-blue-700 transition-all duration-300 transform hover:scale-105 shadow-lg text-base sm:text-lg">
                    <span class="btn-text"><?= $__('auth.reset_password.view.reset_password') ?></span>
                </button>
            </form>

            <!-- Back to Login -->
            <div class="text-center mt-4 sm:mt-6">
                <p class="text-gray-600 text-xs sm:text-sm"><?= $__('auth.reset_password.view.remember_password') ?>
                    <a href="<?= $homeUrl ?>" class="text-blue-600 hover:text-blue-500 font-semibold ml-1"><?= $__('auth.reset_password.view.login_now') ?></a>
                </p>
            </div>
        </div>
    </div>
</div>

<?php $this->endSection(); ?>

<?php $this->startSection('scripts'); ?>
<script>
$(document).ready(function() {
    // Password visibility toggle
    $('.toggle-password').click(function() {
        const target = $(this).data('target');
        const input = $(`input[name="${target}"]`);
        const icon = $(this).find('i');

        if (input.attr('type') === 'password') {
            input.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            input.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });

    // Password strength checker
    function checkPasswordStrength(password) {
        let strength = 0;
        let feedback = [];

        if (password.length >= 8) strength += 1;
        else feedback.push(window.translations?.password_strength?.min_8_chars || 'ít nhất 8 ký tự');

        if (/[A-Z]/.test(password)) strength += 1;
        else feedback.push(window.translations?.password_strength?.uppercase || 'chữ hoa');

        if (/[a-z]/.test(password)) strength += 1;
        else feedback.push(window.translations?.password_strength?.lowercase || 'chữ thường');

        if (/[0-9]/.test(password)) strength += 1;
        else feedback.push(window.translations?.password_strength?.number || 'số');

        if (/[^A-Za-z0-9]/.test(password)) strength += 1;
        else feedback.push(window.translations?.password_strength?.special_char || 'ký tự đặc biệt');

        return { strength, feedback };
    }

    // Update password strength indicator
    function updatePasswordStrength(password) {
        const { strength, feedback } = checkPasswordStrength(password);
        const strengthIndicator = $('#passwordStrength');
        const strengthBar = $('#strengthBar');
        const strengthText = $('#strengthText');

        if (password.length === 0) {
            strengthIndicator.addClass('hidden');
            return;
        }

        strengthIndicator.removeClass('hidden');

        let strengthLabel = '';
        let strengthColor = '';
        let strengthWidth = '';

        if (strength <= 1) {
            strengthLabel = window.translations?.password_strength?.weak || 'yếu';
            strengthColor = 'bg-red-500';
            strengthWidth = '20%';
        } else if (strength <= 2) {
            strengthLabel = window.translations?.password_strength?.weak || 'yếu';
            strengthColor = 'bg-red-400';
            strengthWidth = '40%';
        } else if (strength <= 3) {
            strengthLabel = window.translations?.password_strength?.medium || 'trung bình';
            strengthColor = 'bg-yellow-500';
            strengthWidth = '60%';
        } else if (strength <= 4) {
            strengthLabel = window.translations?.password_strength?.strong || 'mạnh';
            strengthColor = 'bg-green-500';
            strengthWidth = '80%';
        } else {
            strengthLabel = window.translations?.password_strength?.very_strong || 'rất mạnh';
            strengthColor = 'bg-green-600';
            strengthWidth = '100%';
        }

        strengthText.text(strengthLabel);
        strengthBar.removeClass('bg-red-500 bg-red-400 bg-yellow-500 bg-green-500 bg-green-600')
                   .addClass(strengthColor)
                   .css('width', strengthWidth);
    }

    // Password input event
    $('input[name="new_password"]').on('input', function() {
        updatePasswordStrength($(this).val());
    });

    // Reset password form
    $('#resetPasswordForm').submit(function(e) {
        e.preventDefault();

        const newPassword = $('input[name="new_password"]').val();
        const confirmPassword = $('input[name="confirm_password"]').val();

        // Validate passwords match
        if (newPassword !== confirmPassword) {
            GamingApp.showNotification(window.translations?.reset_password_js?.password_mismatch || 'Mật khẩu xác nhận không khớp', 'error');
            return;
        }

        const formData = {
            token: $('input[name="token"]').val(),
            new_password: newPassword,
            confirm_password: confirmPassword,
            csrf_token: $('input[name="csrf_token"]').val()
        };

        const submitBtn = $('button[type="submit"]');
        const originalText = submitBtn.find('.btn-text').text();

        submitBtn.prop('disabled', true).find('.btn-text').text(window.translations?.reset_password_js?.resetting || 'Đang đặt lại...');

        $.ajax({
            url: '/reset-password',
            method: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.status) {
                    GamingApp.showNotification(response.message, 'success');

                    // Redirect to login page after success
                    setTimeout(() => {
                        window.location.href = response.redirect || '<?= $homeUrl ?>';
                    }, 2000);
                } else {
                    GamingApp.showNotification(response.message, 'error');
                    submitBtn.prop('disabled', false).find('.btn-text').text(originalText);
                }
            },
            error: function(xhr) {
                let errorMessage = window.translations?.reset_password_js?.error_occurred || 'Đã xảy ra lỗi. Vui lòng thử lại.';

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.status === 422 && xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = xhr.responseJSON.errors;
                    errorMessage = Object.values(errors).flat().join(', ');
                }

                GamingApp.showNotification(errorMessage, 'error');
                submitBtn.prop('disabled', false).find('.btn-text').text(originalText);
            }
        });
    });

    // Clear error styling on input focus
    $('input').focus(function() {
        $(this).removeClass('border-red-500');
    });
});
</script>
<?php $this->endSection(); ?>

<!-- Include the shared layout -->
<?php include dirname(dirname(dirname(dirname(__DIR__)))) . '/shared/Views/layouts/app.php'; ?>
