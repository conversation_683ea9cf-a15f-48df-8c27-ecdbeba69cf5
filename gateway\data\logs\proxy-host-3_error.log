2025/05/26 14:48:52 [error] 488#488: *219 SSL_do_handshake() failed (SSL: error:0A00010B:SSL routines::wrong version number) while SSL handshaking to upstream, client: **********, server: id.mtfgame.com, request: "GET / HTTP/1.1", upstream: "https://************:8080/", host: "id.mtfgame.com"
2025/05/26 14:48:56 [error] 488#488: *219 SSL_do_handshake() failed (SSL: error:0A00010B:SSL routines::wrong version number) while SSL handshaking to upstream, client: **********, server: id.mtfgame.com, request: "GET / HTTP/1.1", upstream: "https://************:8080/", host: "id.mtfgame.com"
2025/05/26 14:48:56 [error] 488#488: *219 SSL_do_handshake() failed (SSL: error:0A00010B:SSL routines::wrong version number) while SSL handshaking to upstream, client: **********, server: id.mtfgame.com, request: "GET / HTTP/1.1", upstream: "https://************:8080/", host: "id.mtfgame.com"
2025/05/26 14:48:57 [error] 488#488: *219 SSL_do_handshake() failed (SSL: error:0A00010B:SSL routines::wrong version number) while SSL handshaking to upstream, client: **********, server: id.mtfgame.com, request: "GET / HTTP/1.1", upstream: "https://************:8080/", host: "id.mtfgame.com"
2025/05/26 14:48:57 [error] 488#488: *219 SSL_do_handshake() failed (SSL: error:0A00010B:SSL routines::wrong version number) while SSL handshaking to upstream, client: **********, server: id.mtfgame.com, request: "GET / HTTP/1.1", upstream: "https://************:8080/", host: "id.mtfgame.com"
2025/05/26 14:48:58 [error] 488#488: *219 SSL_do_handshake() failed (SSL: error:0A00010B:SSL routines::wrong version number) while SSL handshaking to upstream, client: **********, server: id.mtfgame.com, request: "GET / HTTP/1.1", upstream: "https://************:8080/", host: "id.mtfgame.com"
2025/05/26 14:48:58 [error] 488#488: *219 SSL_do_handshake() failed (SSL: error:0A00010B:SSL routines::wrong version number) while SSL handshaking to upstream, client: **********, server: id.mtfgame.com, request: "GET / HTTP/1.1", upstream: "https://************:8080/", host: "id.mtfgame.com"
2025/05/26 14:49:07 [error] 489#489: *220 SSL_do_handshake() failed (SSL: error:0A00010B:SSL routines::wrong version number) while SSL handshaking to upstream, client: **********, server: id.mtfgame.com, request: "GET / HTTP/1.1", upstream: "https://************:8080/", host: "id.mtfgame.com"
2025/05/28 10:07:34 [warn] 13201#13201: *3144 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/1/00/0000000001 while reading upstream, client: ************, server: id.mtfgame.com, request: "GET /assets/js/tailwind-3.4.1.min.js HTTP/1.1", upstream: "http://**************:8080/assets/js/tailwind-3.4.1.min.js", host: "id.mtfgame.com", referrer: "http://id.mtfgame.com/?return=http%3A%2F%2Fpay.mtfgame.com%2F"
2025/05/28 10:07:37 [warn] 13191#13191: *3160 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/2/00/0000000002 while reading upstream, client: ************, server: id.mtfgame.com, request: "GET /assets/js/tailwind-3.4.1.min.js HTTP/1.1", upstream: "http://**************:8080/assets/js/tailwind-3.4.1.min.js", host: "id.mtfgame.com", referrer: "http://id.mtfgame.com/terms"
2025/05/28 10:07:51 [warn] 13199#13199: *3139 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/3/00/0000000003 while reading upstream, client: ************, server: id.mtfgame.com, request: "GET /assets/js/tailwind-3.4.1.min.js HTTP/1.1", upstream: "http://**************:8080/assets/js/tailwind-3.4.1.min.js", host: "pay.mtfgame.com", referrer: "http://pay.mtfgame.com/"
2025/05/28 10:07:54 [warn] 13191#13191: *3159 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/4/00/0000000004 while reading upstream, client: ************, server: id.mtfgame.com, request: "GET /assets/js/tailwind-3.4.1.min.js HTTP/1.1", upstream: "http://**************:8080/assets/js/tailwind-3.4.1.min.js", host: "id.mtfgame.com", referrer: "http://id.mtfgame.com/?return=http%3A%2F%2Fpay.mtfgame.com%2Fdeposit"
2025/05/28 10:10:23 [warn] 13191#13191: *3261 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/5/00/0000000005 while reading upstream, client: ************, server: id.mtfgame.com, request: "GET /assets/js/tailwind-3.4.1.min.js HTTP/1.1", upstream: "http://**************:8080/assets/js/tailwind-3.4.1.min.js", host: "pay.mtfgame.com", referrer: "http://pay.mtfgame.com/"
2025/05/28 11:09:03 [warn] 17532#17532: *3677 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/6/00/0000000006 while reading upstream, client: ************, server: id.mtfgame.com, request: "GET /assets/js/tailwind-3.4.1.min.js HTTP/1.1", upstream: "http://**************:8080/assets/js/tailwind-3.4.1.min.js", host: "id.mtfgame.com", referrer: "http://id.mtfgame.com/?return=http%3A%2F%2Fpay.mtfgame.com%2F"
2025/05/28 14:29:29 [warn] 17530#17530: *4769 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/7/00/0000000007 while reading upstream, client: ************, server: id.mtfgame.com, request: "GET /assets/js/tailwind-3.4.1.min.js HTTP/1.1", upstream: "http://**************:8080/assets/js/tailwind-3.4.1.min.js", host: "pay.mtfgame.com", referrer: "http://pay.mtfgame.com/deposit"
2025/05/28 14:36:23 [warn] 17530#17530: *4803 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/8/00/0000000008 while reading upstream, client: ************, server: id.mtfgame.com, request: "GET /assets/js/tailwind-3.4.1.min.js HTTP/1.1", upstream: "http://**************:8080/assets/js/tailwind-3.4.1.min.js", host: "pay.mtfgame.com", referrer: "http://pay.mtfgame.com/deposit"
2025/05/28 14:38:19 [warn] 17530#17530: *4800 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/9/00/0000000009 while reading upstream, client: ************, server: id.mtfgame.com, request: "GET /assets/js/tailwind-3.4.1.min.js HTTP/1.1", upstream: "http://**************:8080/assets/js/tailwind-3.4.1.min.js", host: "pay.mtfgame.com", referrer: "http://pay.mtfgame.com/"
2025/05/28 15:12:59 [warn] 17531#17531: *5285 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/0/01/0000000010 while reading upstream, client: ************, server: id.mtfgame.com, request: "GET /assets/js/tailwind-3.4.1.min.js HTTP/1.1", upstream: "http://**************:8080/assets/js/tailwind-3.4.1.min.js", host: "pay.mtfgame.com", referrer: "http://pay.mtfgame.com/"
2025/05/28 15:30:11 [warn] 17531#17531: *5370 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/1/01/0000000011 while reading upstream, client: ************, server: id.mtfgame.com, request: "GET /assets/js/tailwind-3.4.1.min.js HTTP/1.1", upstream: "http://**************:8080/assets/js/tailwind-3.4.1.min.js", host: "pay.mtfgame.com", referrer: "http://pay.mtfgame.com/deposit"
2025/05/28 15:42:06 [warn] 17532#17532: *5493 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/2/01/0000000012 while reading upstream, client: ************, server: id.mtfgame.com, request: "GET /assets/js/tailwind-3.4.1.min.js HTTP/1.1", upstream: "http://**************:8080/assets/js/tailwind-3.4.1.min.js", host: "pay.mtfgame.com", referrer: "http://pay.mtfgame.com/deposit/cancel"
2025/05/28 15:50:20 [warn] 17532#17532: *5562 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/3/01/0000000013 while reading upstream, client: ************, server: id.mtfgame.com, request: "GET /assets/js/tailwind-3.4.1.min.js HTTP/1.1", upstream: "http://**************:8080/assets/js/tailwind-3.4.1.min.js", host: "pay.mtfgame.com", referrer: "http://pay.mtfgame.com/game/adventure_game/guide"
2025/05/28 15:59:28 [warn] 17533#17533: *5981 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/4/01/0000000014 while reading upstream, client: ************, server: id.mtfgame.com, request: "GET /assets/js/tailwind-3.4.1.min.js HTTP/1.1", upstream: "http://**************:8080/assets/js/tailwind-3.4.1.min.js", host: "pay.mtfgame.com", referrer: "http://pay.mtfgame.com/deposit"
2025/05/28 16:04:23 [warn] 17533#17533: *5979 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/5/01/0000000015 while reading upstream, client: ************, server: id.mtfgame.com, request: "GET /assets/js/tailwind-3.4.1.min.js HTTP/1.1", upstream: "http://**************:8080/assets/js/tailwind-3.4.1.min.js", host: "pay.mtfgame.com", referrer: "http://pay.mtfgame.com/deposit"
2025/05/28 16:09:56 [warn] 17534#17534: *6293 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/6/01/0000000016 while reading upstream, client: ************, server: id.mtfgame.com, request: "GET /assets/js/tailwind-3.4.1.min.js HTTP/1.1", upstream: "http://**************:8080/assets/js/tailwind-3.4.1.min.js", host: "pay.mtfgame.com", referrer: "http://pay.mtfgame.com/deposit"
2025/05/28 16:17:28 [warn] 17534#17534: *6475 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/7/01/0000000017 while reading upstream, client: ************, server: id.mtfgame.com, request: "GET /assets/js/tailwind-3.4.1.min.js HTTP/1.1", upstream: "http://**************:8080/assets/js/tailwind-3.4.1.min.js", host: "pay.mtfgame.com", referrer: "http://pay.mtfgame.com/deposit/cancel"
2025/05/28 16:26:55 [warn] 17534#17534: *6994 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/8/01/0000000018 while reading upstream, client: ************, server: id.mtfgame.com, request: "GET /assets/js/tailwind-3.4.1.min.js HTTP/1.1", upstream: "http://**************:8080/assets/js/tailwind-3.4.1.min.js", host: "pay.mtfgame.com", referrer: "http://pay.mtfgame.com/contact"
2025/05/29 11:15:22 [error] 34862#34862: *13939 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: id.mtfgame.com, request: "GET /?return=http%253A%252F%252Fpay.mtfgame.com%252Fdeposit HTTP/1.1", upstream: "http://**************:8080/?return=http%253A%252F%252Fpay.mtfgame.com%252Fdeposit", host: "id.mtfgame.com", referrer: "http://pay.mtfgame.com/"
2025/05/29 11:15:37 [error] 34862#34862: *13939 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: id.mtfgame.com, request: "GET /?return=http%253A%252F%252Fpay.mtfgame.com%252Fdeposit HTTP/1.1", upstream: "http://**************:8080/?return=http%253A%252F%252Fpay.mtfgame.com%252Fdeposit", host: "id.mtfgame.com", referrer: "http://pay.mtfgame.com/"
2025/05/29 11:15:39 [error] 34862#34862: *13939 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: id.mtfgame.com, request: "GET / HTTP/1.1", upstream: "http://**************:8080/", host: "id.mtfgame.com"
2025/05/29 11:15:49 [error] 34862#34862: *13946 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: id.mtfgame.com, request: "GET / HTTP/1.1", upstream: "http://**************:8080/", host: "pay.mtfgame.com"
2025/05/29 11:16:58 [error] 34862#34862: *13946 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: id.mtfgame.com, request: "GET / HTTP/1.1", upstream: "http://**************:8080/", host: "pay.mtfgame.com"
2025/05/29 11:16:59 [error] 34862#34862: *13946 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: id.mtfgame.com, request: "GET / HTTP/1.1", upstream: "http://**************:8080/", host: "pay.mtfgame.com"
2025/05/29 11:17:00 [error] 34862#34862: *13939 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: id.mtfgame.com, request: "GET / HTTP/1.1", upstream: "http://**************:8080/", host: "id.mtfgame.com"
2025/05/29 11:17:00 [error] 34862#34862: *13939 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: id.mtfgame.com, request: "GET / HTTP/1.1", upstream: "http://**************:8080/", host: "id.mtfgame.com"
2025/05/29 11:17:01 [error] 34862#34862: *13939 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: id.mtfgame.com, request: "GET / HTTP/1.1", upstream: "http://**************:8080/", host: "id.mtfgame.com"
2025/05/29 21:27:16 [warn] 179#179: *766 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/1/00/0000000001 while reading upstream, client: ************, server: id.mtfgame.com, request: "GET /assets/js/tailwind-3.4.1.min.js HTTP/1.1", upstream: "http://**************:8080/assets/js/tailwind-3.4.1.min.js", host: "id.mtfgame.com", referrer: "http://id.mtfgame.com/history"
2025/05/29 22:34:02 [warn] 181#181: *1288 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/2/00/0000000002 while reading upstream, client: ************, server: id.mtfgame.com, request: "GET /assets/js/tailwind-3.4.1.min.js HTTP/1.1", upstream: "http://**************:8080/assets/js/tailwind-3.4.1.min.js", host: "id.mtfgame.com", referrer: "http://id.mtfgame.com/register"
2025/05/29 22:39:05 [warn] 182#182: *1372 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/3/00/0000000003 while reading upstream, client: ************, server: id.mtfgame.com, request: "GET /assets/js/tailwind-3.4.1.min.js HTTP/1.1", upstream: "http://**************:8080/assets/js/tailwind-3.4.1.min.js", host: "id.mtfgame.com", referrer: "http://id.mtfgame.com/register"
2025/05/29 23:22:41 [warn] 183#183: *1942 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/4/00/0000000004 while reading upstream, client: ************, server: id.mtfgame.com, request: "GET /assets/js/tailwind-3.4.1.min.js HTTP/1.1", upstream: "http://**************:8080/assets/js/tailwind-3.4.1.min.js", host: "id.mtfgame.com", referrer: "http://id.mtfgame.com/profile"
2025/05/29 23:24:17 [warn] 4515#4515: *2027 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/5/00/0000000005 while reading upstream, client: ************, server: id.mtfgame.com, request: "GET /assets/js/tailwind-3.4.1.min.js HTTP/1.1", upstream: "http://**************:8080/assets/js/tailwind-3.4.1.min.js", host: "id.mtfgame.com", referrer: "http://id.mtfgame.com/"
2025/05/30 07:31:35 [warn] 8856#8856: *3771 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/6/00/0000000006 while reading upstream, client: ************, server: id.mtfgame.com, request: "GET /assets/js/tailwind-3.4.1.min.js HTTP/1.1", upstream: "http://**************:8080/assets/js/tailwind-3.4.1.min.js", host: "id.mtfgame.com", referrer: "http://id.mtfgame.com/profile"
2025/05/30 07:35:45 [warn] 8845#8845: *3801 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/7/00/0000000007 while reading upstream, client: ************, server: id.mtfgame.com, request: "GET /assets/js/tailwind-3.4.1.min.js HTTP/1.1", upstream: "http://**************:8080/assets/js/tailwind-3.4.1.min.js", host: "id.mtfgame.com", referrer: "http://id.mtfgame.com/profile"
2025/05/30 07:38:43 [warn] 8846#8846: *3897 an upstream response is buffered to a temporary file /var/cache/nginx/proxy_temp/8/00/0000000008 while reading upstream, client: ************, server: id.mtfgame.com, request: "GET /assets/js/tailwind-3.4.1.min.js HTTP/1.1", upstream: "http://**************:8080/assets/js/tailwind-3.4.1.min.js", host: "pay.mtfgame.com", referrer: "http://pay.mtfgame.com/"
