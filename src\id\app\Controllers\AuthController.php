<?php

namespace Id\Controllers;

use Core\Controller;
use Core\ValidationException;
use Shared\Services\ApiService;
use Shared\Services\FirebaseService;

/**
 * Authentication Controller for Id Domain
 * Handles login, register, logout functionality
 */
class AuthController extends Controller
{
    private ApiService $apiService;
    private FirebaseService $firebaseService;

    public function __construct()
    {
        parent::__construct();
        $this->apiService = new ApiService();
        $this->firebaseService = new FirebaseService();

        // Override view to use ID domain views
        $idViewPath = __DIR__ . '/../Views/';
        $this->view = new \Core\View($idViewPath);
    }

    /**
     * Process login request
     */
    public function processLogin(): void
    {
        // Clean any previous output
        if (ob_get_level()) {
            ob_clean();
        }

        // Set content type to JSON
        header('Content-Type: application/json');

        // Disable error display for clean JSON output
        $originalErrorReporting = error_reporting(0);
        $originalDisplayErrors = ini_get('display_errors');
        ini_set('display_errors', 0);

        try {
            $loginType = $this->input('login_type', 'account');

            if ($loginType === 'account') {
                $this->processAccountLogin();
            } elseif (in_array($loginType, ['google', 'facebook', 'apple'])) {
                $this->processOAuthLogin($loginType);
            } else {
                throw new \Exception($this->languageHelper->translate('auth.login.controller.invalid_method'));
            }

        } catch (ValidationException $e) {
            $this->json([
                'status' => false,
                'message' => $this->languageHelper->translate('auth.login.controller.validation_required'),
                'errors' => $e->getErrors()
            ], 422);
        } catch (\Exception $e) {
            $this->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 200);
        } finally {
            // Restore error reporting
            error_reporting($originalErrorReporting);
            ini_set('display_errors', $originalDisplayErrors);
        }
    }

    /**
     * Process account login
     */
    private function processAccountLogin(): void
    {
        // Validate CSRF token
        $csrfToken = $this->input('csrf_token');
        if (!$this->securityHelper->verifyCsrfToken($csrfToken)) {
            throw new \Exception($this->languageHelper->translate('auth.login.controller.invalid_token'));
        }

        $data = $this->validate([
            'username' => 'required',
            'password' => 'required'
        ]);

        // Validate with API service
        $errors = $this->apiService->validateLoginData([
            'login_type' => 'account',
            'username' => $data['username'],
            'password' => $data['password']
        ]);

        if (!empty($errors)) {
            throw new ValidationException($errors);
        }

        // Call login API
        $result = $this->apiService->login([
            'login_type' => 'account',
            'username' => $data['username'],
            'password' => $data['password']
        ]);

        // Set user session with balance
        $this->setUserSession($result['user'], $result['token']['access_token']);

        // Determine redirect URL
        $returnUrl = $this->input('return_url') ?? $_GET['return'] ?? null;
        $redirectUrl = $returnUrl ? urldecode($returnUrl) : '/profile';

        $this->json([
            'status' => true,
            'message' => $this->languageHelper->translate('auth.login.controller.success'),
            'redirect' => $redirectUrl,
            'data' => [
                'user' => [
                    'user_id' => $result['user']['user_id'],
                    'username' => $result['user']['username'],
                    'email' => $result['user']['email'],
                    'is_email_verified' => (bool)$result['user']['is_email_verified'],
                    'phone' => $result['user']['phone'],
                    'is_phone_verified' => (bool)$result['user']['is_phone_verified'],
                    'user_type' => $result['user']['user_type'],
                    'balance' => (float)($result['user']['balance'] ?? 0)
                ],
                'token' => $result['token']
            ]
        ]);
    }

    /**
     * Process OAuth login
     */
    private function processOAuthLogin(string $provider): void
    {
        $idToken = $this->input('id_token');

        if (empty($idToken)) {
            throw new \Exception('ID Token không hợp lệ');
        }

        // Call login API
        $result = $this->apiService->login([
            'login_type' => $provider,
            'id_token' => $idToken
        ]);

        // Set user session
        $this->setUserSession($result['user'], $result['token']['access_token']);

        // Determine redirect URL
        $returnUrl = $this->input('return_url');
        $redirectUrl = $returnUrl ? urldecode($returnUrl) : '/profile';

        $this->json([
            'status' => true,
            'message' => 'Đăng nhập thành công',
            'redirect' => $redirectUrl
        ]);
    }

    /**
     * Show register page
     */
    public function register(): void
    {
        // Check if already authenticated
        if ($this->isAuthenticated()) {
            $this->handleReturnUrl();
            return;
        }

        // Generate SEO data
        $seoData = $this->seoHelper->generateSeoData('register');
        $structuredData = $this->seoHelper->generateStructuredData('organization');

        $this->render('auth/register', array_merge($this->getThemeData(), [
            'title' => 'Đăng ký tài khoản',
            'firebase_config' => $this->firebaseService->getFirebaseConfig(),
            'oauth_providers' => $this->firebaseService->getSupportedProviders(),
            'return_url' => $_GET['return'] ?? null,
            'seoData' => $seoData,
            'structuredData' => $structuredData,
            'csrf_token' => $this->securityHelper->generateCsrfToken()
        ]));
    }



    /**
     * Handle return URL after authentication
     */
    private function handleReturnUrl(): void
    {
        $returnUrl = $_GET['return'] ?? $_POST['return_url'] ?? null;

        if ($returnUrl) {
            // Validate return URL is from pay domain
            $decodedUrl = urldecode($returnUrl);
            $payDomain = $this->getDomainUrl('pay');
            if (strpos($decodedUrl, $payDomain) !== false) {
                $this->redirect($decodedUrl);
                return;
            }
        }

        // Default redirect to profile if no valid return URL
        $this->redirect('/profile');
    }

    /**
     * Process registration
     */
    /**
     * Process registration request
     */
    public function processRegister(): void
    {
        try {
            $registerType = $this->input('register_type', 'account');

            if ($registerType === 'account') {
                $this->processAccountRegister();
            } elseif (in_array($registerType, ['google', 'facebook', 'apple'])) {
                $this->processOAuthRegister($registerType);
            } else {
                throw new \Exception($this->languageHelper->translate('auth.register.controller.invalid_method'));
            }

        } catch (ValidationException $e) {
            $this->json([
                'status' => false,
                'message' => $this->languageHelper->translate('auth.register.controller.validation_required'),
                'errors' => $e->getErrors()
            ], 422);
        } catch (\Exception $e) {
            $this->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 200); // Đổi từ 400 thành 200
        }
    }

    /**
     * Process account registration
     */
    private function processAccountRegister(): void
    {
        // Validate CSRF token
        $csrfToken = $this->input('csrf_token');
        if (!$this->securityHelper->verifyCsrfToken($csrfToken)) {
            throw new \Exception($this->languageHelper->translate('auth.register.controller.invalid_token'));
        }

        $data = $this->validate([
            'username' => 'required|min:6|max:20',
            'password' => 'required|min:6|max:30',
            'email' => 'email',
            'phone' => 'phone'
        ]);

        // Additional validation with API service
        $errors = $this->apiService->validateRegistrationData($data);
        if (!empty($errors)) {
            throw new ValidationException($errors);
        }

        // Call register API
        $result = $this->apiService->register([
            'register_type' => 'account',
            'source' => 'web',
            'username' => $data['username'],
            'password' => $data['password'],
            'email' => $data['email'] ?? null,
            'phone' => $data['phone'] ?? null,
            'device_info' => $this->getDeviceInfo()
        ]);

        // Set user session
        $this->setUserSession($result['user'], $result['token']['access_token']);

        $this->json([
            'status' => true,
            'message' => $this->languageHelper->translate('auth.register.controller.success'),
            'redirect' => '/'
        ]);
    }

    /**
     * Process OAuth registration
     */
    private function processOAuthRegister(string $provider): void
    {
        $idToken = $this->input('id_token');

        if (empty($idToken)) {
            throw new \Exception('ID Token không hợp lệ');
        }

        // Verify Firebase token
        $firebaseUser = $this->firebaseService->verifyIdToken($idToken);

        // Call register API
        $result = $this->apiService->register([
            'register_type' => $provider,
            'source' => 'web',
            'id_token' => $idToken,
            'device_info' => $this->getDeviceInfo()
        ]);

        // Set user session
        $this->setUserSession($result['user'], $result['token']['access_token']);

        $this->json([
            'status' => true,
            'message' => 'Đăng ký thành công',
            'redirect' => '/'
        ]);
    }

    /**
     * Show forgot password page
     */
    public function forgotPassword(): void
    {
        // Generate SEO data
        $seoData = $this->seoHelper->generateSeoData('forgot-password');
        $structuredData = $this->seoHelper->generateStructuredData('organization');

        // Generate captcha
        $captcha = $this->securityHelper->generateCaptcha();
        $captchaImage = $this->securityHelper->generateCaptchaImage($captcha);

        $this->render('auth/forgot-password', array_merge($this->getThemeData(), [
            'title' => 'Quên mật khẩu',
            'seoData' => $seoData,
            'structuredData' => $structuredData,
            'csrf_token' => $this->securityHelper->generateCsrfToken(),
            'captcha_image' => $captchaImage
        ]));
    }

   /**
     * Process forgot password request
     */
    public function processForgotPassword(): void
    {
        try {
            // Validate CSRF token
            $csrfToken = $this->input('csrf_token');
            if (!$this->securityHelper->verifyCsrfToken($csrfToken)) {
                throw new \Exception('Token bảo mật không hợp lệ');
            }

            // Validate captcha
            $captchaInput = $this->input('captcha');
            if (!$this->securityHelper->verifyCaptcha($captchaInput)) {
                throw new \Exception('Mã xác thực không đúng');
            }

            $data = $this->validate([
                'email' => 'required|email'
            ]);

            $this->apiService->forgotPassword($data['email']);

            $this->json([
                'status' => true,
                'message' => 'Yêu cầu khôi phục mật khẩu đã được gửi. Vui lòng kiểm tra email của bạn.'
            ]);

        } catch (ValidationException $e) {
            $this->json([
                'status' => false,
                'message' => 'Dữ liệu không hợp lệ',
                'errors' => $e->getErrors()
            ], 422);
        } catch (\Exception $e) {
            $this->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 200); // Đổi từ 400 thành 200
        }
    }

    /**
     * Show reset password page
     */
    public function resetPassword(): void
    {
        // Check if already authenticated
        if ($this->isAuthenticated()) {
            $this->handleReturnUrl();
            return;
        }

        // Get token from URL
        $token = $_GET['token'] ?? null;
        if (!$token) {
            $this->redirect('/forgot-password');
            return;
        }

        // Generate SEO data
        $seoData = $this->seoHelper->generateSeoData('reset-password');
        $structuredData = $this->seoHelper->generateStructuredData('organization');

        $this->render('auth/reset-password', array_merge($this->getThemeData(), [
            'title' => 'Đặt lại mật khẩu',
            'seoData' => $seoData,
            'structuredData' => $structuredData,
            'csrf_token' => $this->securityHelper->generateCsrfToken(),
            'reset_token' => $token
        ]));
    }

    /**
     * Process reset password request
     */
    public function processResetPassword(): void
    {
        try {
            // Validate CSRF token
            $csrfToken = $this->input('csrf_token');
            if (!$this->securityHelper->verifyCsrfToken($csrfToken)) {
                throw new \Exception('Token bảo mật không hợp lệ');
            }

            $data = $this->validate([
                'token' => 'required',
                'new_password' => 'required|min:6|max:30',
                'confirm_password' => 'required'
            ]);

            // Check if passwords match
            if ($data['new_password'] !== $data['confirm_password']) {
                throw new \Exception('Mật khẩu xác nhận không khớp');
            }

            $this->apiService->resetPassword(
                $data['token'],
                $data['new_password'],
                $data['confirm_password']
            );

            $this->json([
                'status' => true,
                'message' => 'Mật khẩu đã được đặt lại thành công. Vui lòng đăng nhập lại với mật khẩu mới.',
                'redirect' => $this->getHomeUrl()
            ]);

        } catch (ValidationException $e) {
            $this->json([
                'status' => false,
                'message' => 'Dữ liệu không hợp lệ',
                'errors' => $e->getErrors()
            ], 422);
        } catch (\Exception $e) {
            $this->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 200);
        }
    }

    /**
     * Refresh captcha
     */
    public function refreshCaptcha(): void
    {
        try {
            $captchaData = $this->securityHelper->refreshCaptcha();

            $this->json([
                'status' => true,
                'data' => $captchaData
            ]);
        } catch (\Exception $e) {
            $this->json([
                'status' => false,
                'message' => 'Không thể tạo mã xác thực mới'
            ], 500);
        }
    }

    /**
     * Logout user
     */
    public function logout(): void
    {
        $this->clearUserSession();

        // Handle return URL like login
        $returnUrl = $_GET['return'] ?? null;

        if ($returnUrl) {
            $decodedUrl = urldecode($returnUrl);
            // Validate return URL is from allowed domains
            $payDomain = $this->getDomainUrl('pay');
            $idDomain = $this->getDomainUrl('id');
            if (strpos($decodedUrl, parse_url($payDomain, PHP_URL_HOST)) !== false ||
                strpos($decodedUrl, parse_url($idDomain, PHP_URL_HOST)) !== false) {
                $this->redirect($decodedUrl);
                return;
            }
        }

        // Fallback: Check referer
        $referer = $_SERVER['HTTP_REFERER'] ?? '';
        $payHost = parse_url($this->getDomainUrl('pay'), PHP_URL_HOST);
        if (strpos($referer, $payHost) !== false) {
            $this->redirect($this->getDomainUrl('pay'));
        } else {
            $this->redirect('/');
        }
    }

    /**
     * Get device information
     */
    private function getDeviceInfo(): array
    {
        return [
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'ip_address' => $this->getRealIpAddress(),
            'platform' => 'web'
        ];
    }
}
