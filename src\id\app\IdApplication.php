<?php

namespace Id\App;

use Core\Application;
use Core\Router;

/**
 * Id Domain Application
 * Handles id.mtfgame.com - Auth, Profile & History functionality
 */
class IdApplication extends Application
{
    public function __construct(array $config = [])
    {
        parent::__construct($config);

        // Set view path for id domain
        $idViewPath = __DIR__ . '/Views/';
        $this->view = new \Core\View($idViewPath);
    }

    /**
     * Setup routes for id domain
     */
    protected function setupRoutes(): void
    {
        // Get router from parent
        $router = $this->getRouter();

        // Root route - handle login form or redirect to profile
        $router->get('/', 'Id\Controllers\IndexController@index');
        $router->post('/', 'Id\Controllers\AuthController@processLogin'); // Handle form submission from root

        // Public routes (guest only)
        $router->group(['middleware' => 'guest'], function($router) {
            $router->get('/register', 'Id\Controllers\AuthController@register');
            $router->post('/register', 'Id\Controllers\AuthController@processRegister');
            $router->get('/forgot-password', 'Id\Controllers\AuthController@forgotPassword');
            $router->post('/forgot-password', 'Id\Controllers\AuthController@processForgotPassword');
            $router->post('/refresh-captcha', 'Id\Controllers\AuthController@refreshCaptcha');
        });

        // Protected routes (require authentication)
        $router->group(['middleware' => 'auth'], function($router) {
            $router->get('/profile', 'Id\Controllers\ProfileController@index');
            $router->post('/profile/send-email-verification', 'Id\Controllers\ProfileController@sendEmailVerification');
            $router->post('/profile/verify-email', 'Id\Controllers\ProfileController@verifyEmail');
            $router->post('/profile/send-phone-verification', 'Id\Controllers\ProfileController@sendPhoneVerification');
            $router->post('/profile/verify-phone', 'Id\Controllers\ProfileController@verifyPhone');
            $router->post('/profile/change-password', 'Id\Controllers\ProfileController@changePassword');
            $router->get('/history', 'Id\Controllers\HistoryController@index');
        });

        // Logout route (no middleware needed)
        $router->get('/logout', 'Id\Controllers\AuthController@logout');

        // Policy routes (public access) - using shared controller
        $router->get('/terms', 'Shared\Controllers\PolicyController@terms');
        $router->get('/privacy', 'Shared\Controllers\PolicyController@privacy');
        $router->get('/contact', 'Shared\Controllers\PolicyController@contact');
        $router->post('/contact', 'Shared\Controllers\PolicyController@submitContact');

        // API routes
        $router->group(['prefix' => 'api'], function($router) {
            $router->post('/auth/verify-token', 'Id\Controllers\Api\AuthController@verifyToken');
            $router->post('/user/send-phone-verification', 'Id\Controllers\Api\UserController@sendPhoneVerification');
            $router->post('/user/verify-phone', 'Id\Controllers\Api\UserController@verifyPhone');
            $router->post('/refresh-balance', 'Shared\Controllers\Api\BalanceController@refresh');

            // Settings routes
            $router->post('/settings/language', 'Shared\Controllers\SettingsController@switchLanguage');
            $router->get('/settings', 'Shared\Controllers\SettingsController@getSettings');
        });

        // SEO routes
        $router->get('/sitemap.xml', 'Shared\Controllers\SitemapController@index');
    }



    /**
     * Run the id application
     */
    public function run(): void
    {
        // Use parent's run method which handles everything correctly
        parent::run();
    }
}
