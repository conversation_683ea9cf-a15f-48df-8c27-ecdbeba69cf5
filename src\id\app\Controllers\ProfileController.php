<?php

namespace Id\Controllers;

use Core\Controller;
use Core\ValidationException;
use Shared\Services\ApiService;

/**
 * Profile Controller for Id Domain
 * Handles user profile management
 */
class ProfileController extends Controller
{
    private ApiService $apiService;

    public function __construct()
    {
        parent::__construct();
        $this->apiService = new ApiService();

        // Override view to use ID domain views
        $idViewPath = __DIR__ . '/../Views/';
        $this->view = new \Core\View($idViewPath);
    }

    /**
     * Show profile page
     */
    public function index(): void
    {
        $user = $this->getUser();

        // Generate SEO data
        $seoData = $this->seoHelper->generateSeoData('profile');
        $structuredData = $this->seoHelper->generateStructuredData('organization');

        $this->render('profile/index', array_merge($this->getThemeData(), [
            'title' => 'Thông tin tài kho<PERSON>n',
            'user' => $user,
            'seoData' => $seoData,
            'structuredData' => $structuredData,
            'csrf_token' => $this->securityHelper->generateCsrfToken()
        ]));
    }

    /**
     * Send email verification code
     */
    public function sendEmailVerification(): void
    {
        header('Content-Type: application/json');

        try {
            if (!$this->isAuthenticated()) {
                throw new \Exception('Unauthorized');
            }

            // Validate CSRF token
            $csrfToken = $this->input('csrf_token');
            if (!$this->securityHelper->verifyCsrfToken($csrfToken)) {
                throw new \Exception('Token bảo mật không hợp lệ');
            }

            $data = $this->validate([
                'email' => 'required|email'
            ]);

            $token = $this->getAccessToken();
            $result = $this->apiService->sendEmailVerification($data['email'], $token);

            $this->json([
                'status' => true,
                'message' => $this->languageHelper->translate('profile.controller.email_sent'),
                'data' => $result
            ]);

        } catch (ValidationException $e) {
            $this->json([
                'status' => false,
                'message' => 'Dữ liệu không hợp lệ',
                'errors' => $e->getErrors()
            ], 422);
        } catch (\Exception $e) {
            $this->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Verify email with code
     */
    public function verifyEmail(): void
    {
        header('Content-Type: application/json');

        try {
            if (!$this->isAuthenticated()) {
                throw new \Exception('Unauthorized');
            }

            // Validate CSRF token
            $csrfToken = $this->input('csrf_token');
            if (!$this->securityHelper->verifyCsrfToken($csrfToken)) {
                throw new \Exception('Token bảo mật không hợp lệ');
            }

            $data = $this->validate([
                'email' => 'required|email',
                'verification_code' => 'required|string'
            ]);

            $token = $this->getAccessToken();
            $result = $this->apiService->verifyEmail($data['email'], $data['verification_code'], $token);

            // Update user session with new verification status
            if (isset($result['user'])) {
                $this->setUserSession($result['user'], $token);
            }

            $this->json([
                'status' => true,
                'message' => $this->languageHelper->translate('profile.controller.email_verified'),
                'data' => $result,
                'reload' => true // Signal frontend to reload page
            ]);

        } catch (ValidationException $e) {
            $this->json([
                'status' => false,
                'message' => 'Dữ liệu không hợp lệ',
                'errors' => $e->getErrors()
            ], 422);
        } catch (\Exception $e) {
            $this->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Send phone verification code
     */
    public function sendPhoneVerification(): void
    {
        header('Content-Type: application/json');

        try {
            if (!$this->isAuthenticated()) {
                throw new \Exception('Unauthorized');
            }

            // Validate CSRF token
            $csrfToken = $this->input('csrf_token');
            if (!$this->securityHelper->verifyCsrfToken($csrfToken)) {
                throw new \Exception($this->languageHelper->translate('profile.controller.invalid_token'));
            }

            $data = $this->validate([
                'phone' => 'required|phone'
            ]);

            $token = $this->getAccessToken();
            $result = $this->apiService->sendPhoneVerification($data['phone'], $token);

            $this->json([
                'status' => true,
                'message' => $this->languageHelper->translate('profile.controller.phone_sent'),
                'data' => $result
            ]);

        } catch (ValidationException $e) {
            $this->json([
                'status' => false,
                'message' => $this->languageHelper->translate('profile.controller.validation_required'),
                'errors' => $e->getErrors()
            ], 422);
        } catch (\Exception $e) {
            $this->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Verify phone with OTP
     */
    public function verifyPhone(): void
    {
        header('Content-Type: application/json');

        try {
            if (!$this->isAuthenticated()) {
                throw new \Exception('Unauthorized');
            }

            // Validate CSRF token
            $csrfToken = $this->input('csrf_token');
            if (!$this->securityHelper->verifyCsrfToken($csrfToken)) {
                throw new \Exception('Token bảo mật không hợp lệ');
            }

            $data = $this->validate([
                'phone' => 'required|phone',
                'otp_code' => 'required|string'
            ]);

            $token = $this->getAccessToken();
            $result = $this->apiService->verifyPhoneOTP($data['phone'], $data['otp_code'], $token);

            // Update user session with new verification status
            if (isset($result['user'])) {
                $this->setUserSession($result['user'], $token);
            }

            $this->json([
                'status' => true,
                'message' => $this->languageHelper->translate('profile.controller.phone_verified'),
                'data' => $result,
                'reload' => true // Signal frontend to reload page
            ]);

        } catch (ValidationException $e) {
            $this->json([
                'status' => false,
                'message' => 'Dữ liệu không hợp lệ',
                'errors' => $e->getErrors()
            ], 422);
        } catch (\Exception $e) {
            $this->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Change password
     */
    public function changePassword(): void
    {
        header('Content-Type: application/json');

        try {
            if (!$this->isAuthenticated()) {
                throw new \Exception('Unauthorized');
            }

            // Validate CSRF token
            $csrfToken = $this->input('csrf_token');
            if (!$this->securityHelper->verifyCsrfToken($csrfToken)) {
                throw new \Exception('Token bảo mật không hợp lệ');
            }

            $data = $this->validate([
                'current_password' => 'required|string',
                'new_password' => 'required|string|min:6|max:30',
                'confirm_password' => 'required|string'
            ]);

            // Check if new password and confirm password match
            if ($data['new_password'] !== $data['confirm_password']) {
                $this->json([
                    'status' => false,
                    'message' => $this->languageHelper->translate('profile.controller.password_mismatch')
                ], 422);
                return;
            }

            $token = $this->getAccessToken();
            $result = $this->apiService->changePassword(
                $data['current_password'],
                $data['new_password'],
                $data['confirm_password'],
                $token
            );

            // Clear user session after password change for security
            $this->clearUserSession();

            // Set flash message for login page
            $this->setFlash('success', $this->languageHelper->translate('profile.controller.password_changed_login_required'));

            $this->json([
                'status' => true,
                'message' => $this->languageHelper->translate('profile.controller.password_changed'),
                'data' => $result,
                'redirect' => $this->getLoginUrl() // Signal frontend to redirect to login
            ]);

        } catch (ValidationException $e) {
            $this->json([
                'status' => false,
                'message' => 'Dữ liệu không hợp lệ',
                'errors' => $e->getErrors()
            ], 422);
        } catch (\Exception $e) {
            $this->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }
}
