<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- SEO Meta Tags -->
    <title><?= $this->escape($seoData['title'] ?? $title ?? 'MtfGame - Gaming Account Manager') ?></title>
    <meta name="description" content="<?= $this->escape($seoData['description'] ?? 'MtfGame - Hệ thống quản lý tài khoản game chuyên nghiệp với nạp tiền nhanh chóng và bảo mật cao.') ?>">
    <meta name="keywords" content="<?= $this->escape($seoData['keywords'] ?? 'game, nạp tiền, gaming, tài khoản game, mtfgame') ?>">
    <meta name="author" content="MtfGame">
    <meta name="robots" content="<?= $seoData['robots'] ?? 'index, follow' ?>">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="<?= $this->csrfToken() ?>">

    <!-- Canonical URL -->
    <?php if (isset($seoData['canonical'])): ?>
    <link rel="canonical" href="<?= $this->escape($seoData['canonical']) ?>">
    <?php endif; ?>

    <!-- Open Graph Tags -->
    <meta property="og:title" content="<?= $this->escape($seoData['og_title'] ?? $seoData['title'] ?? $title ?? 'MtfGame') ?>">
    <meta property="og:description" content="<?= $this->escape($seoData['og_description'] ?? $seoData['description'] ?? 'MtfGame - Hệ thống quản lý tài khoản game chuyên nghiệp') ?>">
    <meta property="og:type" content="<?= $seoData['og_type'] ?? 'website' ?>">
    <meta property="og:url" content="<?= $this->escape($seoData['og_url'] ?? $seoData['canonical'] ?? '') ?>">
    <meta property="og:image" content="<?= $this->escape($seoData['og_image'] ?? $this->asset('images/logo_mtf.png')) ?>">
    <meta property="og:site_name" content="MtfGame">
    <meta property="og:locale" content="vi_VN">

    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?= $this->escape($seoData['og_title'] ?? $seoData['title'] ?? $title ?? 'MtfGame') ?>">
    <meta name="twitter:description" content="<?= $this->escape($seoData['og_description'] ?? $seoData['description'] ?? 'MtfGame - Hệ thống quản lý tài khoản game chuyên nghiệp') ?>">
    <meta name="twitter:image" content="<?= $this->escape($seoData['og_image'] ?? $this->asset('images/logo_mtf.png')) ?>">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= $this->asset('images/favicon.ico') ?>">
    <link rel="apple-touch-icon" href="<?= $this->asset('images/logo_mtf.png') ?>">




    <!-- Structured Data -->
    <?php if (isset($structuredData) && !empty($structuredData)): ?>
    <script type="application/ld+json">
    <?= $structuredData ?>
    </script>
    <?php endif; ?>

    <!-- Tailwind CSS -->
    <script src="<?= $this->asset('js/tailwind-3.4.1.min.js') ?>"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        },
                        emerald: {
                            50: '#ecfdf5',
                            100: '#d1fae5',
                            200: '#a7f3d0',
                            300: '#6ee7b7',
                            400: '#34d399',
                            500: '#10b981',
                            600: '#059669',
                            700: '#047857',
                            800: '#065f46',
                            900: '#064e3b',
                        }
                    },
                    fontFamily: {
                        'sans': ['Inter', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
                        'gaming': ['JetBrains Mono', 'Consolas', 'Monaco', 'Courier New', 'monospace'],
                        'display': ['Inter', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
                        'mono': ['JetBrains Mono', 'Consolas', 'Monaco', 'Courier New', 'monospace']
                    }
                }
            }
        }
    </script>

    <!-- Font Awesome -->
    <link href="<?= $this->asset('css/font-awesome-6.4.0.min.css') ?>" rel="stylesheet">

    <!-- Custom Font CSS -->
    <link href="<?= $this->asset('css/fonts.css') ?>" rel="stylesheet">

    <!-- Select2 CSS -->
    <link href="<?= $this->asset('css/select2.min.css') ?>" rel="stylesheet" />
    <link href="<?= $this->asset('css/select2-bootstrap-5-theme.min.css') ?>" rel="stylesheet" />

    <!-- Custom CSS -->
    <style>
        /* CSS Variables for Gaming Theme - White Background */
        :root {
            --primary-color: #ff6b35; /* Orange gaming accent */
            --secondary-color: #f8f9fa; /* Light gray */
            --accent-color: #ff6b35; /* Same as primary for consistency */
            --background-color: #ffffff; /* White background */
            --surface-color: #ffffff; /* White surface */
            --text-color: #1f2937; /* Dark gray text */
            --text-muted: #6b7280; /* Muted gray text */
            --border-color: #e5e7eb; /* Light gray border */
            --success-color: #22c55e; /* Green */
            --danger-color: #ef4444; /* Red */
            --warning-color: #f59e0b; /* Amber */
        }

        /* Font Loading and Fallbacks */
        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }

        /* Real-time Validation Styles */
        .validation-message {
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .validation-icon {
            animation: fadeIn 0.2s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Input focus states with validation */
        input.border-green-500:focus {
            border-color: #10b981 !important;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
        }

        input.border-red-500:focus {
            border-color: #ef4444 !important;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
        }

        /* Password strength indicator */
        .password-strength {
            height: 4px;
            border-radius: 2px;
            transition: all 0.3s ease;
        }

        .password-strength.weak { background-color: #ef4444; width: 25%; }
        .password-strength.medium { background-color: #f59e0b; width: 50%; }
        .password-strength.strong { background-color: #10b981; width: 75%; }
        .password-strength.very-strong { background-color: #059669; width: 100%; }

        /* Gaming/Display Font Classes */
        .font-gaming, .font-display {
            font-family: 'JetBrains Mono', Consolas, Monaco, 'Courier New', monospace !important;
        }

        /* Mono Font Class */
        .font-mono {
            font-family: 'JetBrains Mono', Consolas, Monaco, 'Courier New', monospace !important;
        }

        /* Ensure consistent font rendering */
        body, html {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        /* Fix font issues in specific elements */
        input, textarea, select, button {
            font-family: inherit;
        }

        /* Gaming theme specific font weights */
        .font-gaming {
            font-weight: 500;
            letter-spacing: 0.025em;
        }

        /* Auth pages styling */
        .auth-container {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Form container */
        .auth-form-container {
            background: white;
            border: 1px solid #e5e7eb;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        /* Responsive adjustments */
        @media (max-height: 600px) {
            .auth-container {
                min-height: auto;
                padding: 2rem 0;
            }
        }

        @media (max-width: 640px) {
            .auth-container {
                padding: 1rem;
            }
        }

        /* Select2 Custom Styling */
        .select2-container--bootstrap-5 .select2-selection {
            background-color: #ffffff !important;
            border: 1px solid #d1d5db !important;
            border-radius: 0.5rem !important;
            min-height: 2.5rem !important;
            font-size: 0.875rem !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single {
            background-color: #ffffff !important;
            padding: 0.375rem 0.75rem !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
            background-color: transparent !important;
            padding: 0 !important;
            line-height: 1.5rem !important;
            color: #374151 !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
            background-color: transparent !important;
            height: 2.5rem !important;
            right: 0.75rem !important;
        }

        .select2-container--bootstrap-5.select2-container--focus .select2-selection {
            background-color: #ffffff !important;
            border-color: #3b82f6 !important;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
        }

        .select2-dropdown {
            background-color: #ffffff !important;
            border: 1px solid #d1d5db !important;
            border-radius: 0.5rem !important;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1) !important;
            z-index: 9999 !important;
        }

        .select2-container--bootstrap-5 .select2-results {
            background-color: #ffffff !important;
        }

        .select2-container--bootstrap-5 .select2-results__options {
            background-color: #ffffff !important;
        }

        .select2-container--bootstrap-5 .select2-results__option {
            background-color: #ffffff !important;
            padding: 0.5rem 0.75rem !important;
            font-size: 0.875rem !important;
            color: #374151 !important;
        }

        .select2-container--bootstrap-5 .select2-results__option:hover {
            background-color: #f3f4f6 !important;
            color: #374151 !important;
        }

        .select2-container--bootstrap-5 .select2-results__option--highlighted {
            background-color: #3b82f6 !important;
            color: #ffffff !important;
        }

        .select2-container--bootstrap-5 .select2-results__option--selected {
            background-color: #eff6ff !important;
            color: #1d4ed8 !important;
        }

        /* Search box styling */
        .select2-search--dropdown .select2-search__field {
            background-color: #ffffff !important;
            border: 1px solid #d1d5db !important;
            border-radius: 0.375rem !important;
            padding: 0.375rem 0.75rem !important;
            font-size: 0.875rem !important;
            color: #374151 !important;
        }

        .select2-search--dropdown .select2-search__field:focus {
            border-color: #3b82f6 !important;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
            outline: none !important;
        }
    </style>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>


</head>
<body class="bg-white font-sans min-h-screen flex flex-col">
    <!-- Header -->
    <?php $this->include('partials.header'); ?>

    <!-- Flash Messages (Fixed Position) -->
    <?= $this->flash() ?>

    <!-- Main Content -->
    <main class="flex-1">
        <!-- Page Content -->
        <?= $this->yieldSection('content') ?>
    </main>

    <!-- Footer -->
    <?php $this->include('partials.footer'); ?>

    <!-- jQuery -->
    <script src="<?= $this->asset('js/jquery-3.7.0.min.js') ?>"></script>

    <!-- Select2 JS -->
    <script src="<?= $this->asset('js/select2.min.js') ?>"></script>

    <!-- Pass domain data to JavaScript -->
    <script>
        window.GamingApp = window.GamingApp || {};
        window.GamingApp.config = window.GamingApp.config || {};
        window.GamingApp.config.domains = <?= json_encode($domainData['domains'] ?? []) ?>;
        window.GamingApp.config.api = {
            refreshBalance: '/api/refresh-balance'
        };

        // Pass translations to JavaScript
        window.translations = {
            validation: {
                required: '<?= $__('shared.validation.required') ?>',
                email_invalid: '<?= $__('shared.validation.email_invalid') ?>',
                phone_invalid: '<?= $__('shared.validation.phone_invalid') ?>',
                username_min: '<?= $__('shared.validation.username_min') ?>',
                username_max: '<?= $__('shared.validation.username_max') ?>',
                username_format: '<?= $__('shared.validation.username_format') ?>',
                password_min: '<?= $__('shared.validation.password_min') ?>',
                password_max: '<?= $__('shared.validation.password_max') ?>',
                password_enter_first: '<?= $__('shared.validation.password_enter_first') ?>',
                password_mismatch: '<?= $__('shared.validation.password_mismatch') ?>'
            },
            balance: {
                wait_seconds: '<?= $__('shared.balance.wait_seconds') ?>',
                updated: '<?= $__('shared.balance.updated') ?>',
                update_failed: '<?= $__('shared.balance.update_failed') ?>',
                connection_error: '<?= $__('shared.balance.connection_error') ?>'
            },
            password_strength: {
                weak: '<?= $__('shared.password_strength.weak') ?>',
                medium: '<?= $__('shared.password_strength.medium') ?>',
                strong: '<?= $__('shared.password_strength.strong') ?>',
                very_strong: '<?= $__('shared.password_strength.very_strong') ?>',
                need_more: '<?= $__('shared.password_strength.need_more') ?>',
                good: '<?= $__('shared.password_strength.good') ?>',
                min_8_chars: '<?= $__('shared.password_strength.min_8_chars') ?>',
                uppercase: '<?= $__('shared.password_strength.uppercase') ?>',
                lowercase: '<?= $__('shared.password_strength.lowercase') ?>',
                number: '<?= $__('shared.password_strength.number') ?>',
                special_char: '<?= $__('shared.password_strength.special_char') ?>'
            },
            register_js: {
                hide_contact_info: '<?= $__('shared.register_js.hide_contact_info') ?>',
                add_contact_info: '<?= $__('shared.register_js.add_contact_info') ?>',
                password_mismatch: '<?= $__('shared.register_js.password_mismatch') ?>',
                creating_account: '<?= $__('shared.register_js.creating_account') ?>',
                firebase_not_initialized: '<?= $__('shared.register_js.firebase_not_initialized') ?>',
                google_register_failed: '<?= $__('shared.register_js.google_register_failed') ?>',
                facebook_register_failed: '<?= $__('shared.register_js.facebook_register_failed') ?>',
                error_occurred: '<?= $__('shared.register_js.error_occurred') ?>'
            },
            login_js: {
                firebase_not_initialized: '<?= $__('shared.login_js.firebase_not_initialized') ?>',
                google_login_failed: '<?= $__('shared.login_js.google_login_failed') ?>',
                facebook_login_failed: '<?= $__('shared.login_js.facebook_login_failed') ?>',
                logging_in: '<?= $__('shared.login_js.logging_in') ?>',
                login: '<?= $__('shared.login_js.login') ?>',
                error_occurred: '<?= $__('shared.login_js.error_occurred') ?>'
            },
            forgot_password_js: {
                click_to_refresh: '<?= $__('shared.forgot_password_js.click_to_refresh') ?>',
                refresh_captcha: '<?= $__('shared.forgot_password_js.refresh_captcha') ?>',
                captcha_instruction: '<?= $__('shared.forgot_password_js.captcha_instruction') ?>',
                remember_password: '<?= $__('shared.forgot_password_js.remember_password') ?>',
                login_now: '<?= $__('shared.forgot_password_js.login_now') ?>',
                sending: '<?= $__('shared.forgot_password_js.sending') ?>',
                captcha_refresh_failed: '<?= $__('shared.forgot_password_js.captcha_refresh_failed') ?>',
                captcha_error_check: '<?= $__('shared.forgot_password_js.captcha_error_check') ?>',
                error_occurred: '<?= $__('shared.forgot_password_js.error_occurred') ?>'
            }
        };
    </script>

    <!-- Font Loading Script -->
    <script>
        // Font loading optimization
        if ('fonts' in document) {
            Promise.all([
                document.fonts.load('400 1em Inter'),
                document.fonts.load('500 1em Inter'),
                document.fonts.load('600 1em Inter'),
                document.fonts.load('400 1em JetBrains Mono'),
                document.fonts.load('500 1em JetBrains Mono')
            ]).then(function() {
                document.documentElement.classList.add('font-loaded');
                console.log('Fonts loaded successfully');
            }).catch(function() {
                console.log('Font loading failed, using fallbacks');
                document.documentElement.classList.add('font-fallback');
            });
        } else {
            // Fallback for browsers without Font Loading API
            document.documentElement.classList.add('font-fallback');
        }
    </script>

    <!-- Custom JS -->
    <script src="<?= $this->asset('js/main.js') ?>"></script>

    <!-- Firebase Config -->
    <?php

    if (isset($firebase_config) && isset($firebaseService)): ?>
    <script>
        <?= $firebaseService->getFirebaseInitScript() ?>
    </script>
    <?php endif; ?>

    <!-- Page Specific Scripts -->
    <?= $this->yieldSection('scripts', '') ?>
</body>
</html>
